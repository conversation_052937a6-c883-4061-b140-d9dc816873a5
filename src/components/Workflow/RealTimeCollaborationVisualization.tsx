'use client';

import React, { useState, useEffect } from 'react';
import { 
  UsersIcon, 
  ChatBubbleLeftRightIcon, 
  LightBulbIcon, 
  ClockIcon,
  ArrowPathIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline';

interface AgentActivity {
  agentId: string;
  status: 'idle' | 'analyzing' | 'responding' | 'completed';
  currentTask?: string;
  progress: number;
  lastActivity: string;
  insights: Array<{
    id: string;
    content: string;
    confidence: number;
    timestamp: string;
  }>;
}

interface CollaborationEvent {
  id: string;
  type: 'consultation_started' | 'insight_generated' | 'consultation_completed' | 'agent_handoff';
  agentId: string;
  targetAgentId?: string;
  content: string;
  timestamp: string;
  metadata?: any;
}

interface RealTimeCollaborationVisualizationProps {
  executionId?: string;
  className?: string;
}

export default function RealTimeCollaborationVisualization({
  executionId,
  className = ''
}: RealTimeCollaborationVisualizationProps) {
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([]);
  const [collaborationEvents, setCollaborationEvents] = useState<CollaborationEvent[]>([]);
  const [isLive, setIsLive] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');

  useEffect(() => {
    // Initialize with mock data for demonstration
    initializeMockData();
    
    // Simulate real-time updates
    const interval = setInterval(() => {
      if (isLive) {
        simulateRealTimeUpdates();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isLive]);

  const initializeMockData = () => {
    const mockAgents: AgentActivity[] = [
      {
        agentId: 'seo-keyword',
        status: 'analyzing',
        currentTask: 'Analyzing keyword density and search intent',
        progress: 65,
        lastActivity: new Date().toISOString(),
        insights: [
          {
            id: 'insight-1',
            content: 'Primary keyword "AI healthcare" has good search volume (8.1K/month)',
            confidence: 0.92,
            timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString()
          }
        ]
      },
      {
        agentId: 'content-strategy',
        status: 'responding',
        currentTask: 'Optimizing content structure based on SEO insights',
        progress: 40,
        lastActivity: new Date(Date.now() - 1 * 60 * 1000).toISOString(),
        insights: [
          {
            id: 'insight-2',
            content: 'Recommend adding H2 section on "AI Diagnostic Tools" for better structure',
            confidence: 0.88,
            timestamp: new Date(Date.now() - 3 * 60 * 1000).toISOString()
          }
        ]
      },
      {
        agentId: 'market-research',
        status: 'completed',
        currentTask: 'Market analysis complete',
        progress: 100,
        lastActivity: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        insights: [
          {
            id: 'insight-3',
            content: 'Healthcare AI market growing at 40.2% CAGR, focus on patient care benefits',
            confidence: 0.95,
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
          }
        ]
      }
    ];

    const mockEvents: CollaborationEvent[] = [
      {
        id: 'event-1',
        type: 'consultation_started',
        agentId: 'seo-keyword',
        content: 'SEO agent consultation initiated for keyword optimization',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()
      },
      {
        id: 'event-2',
        type: 'insight_generated',
        agentId: 'market-research',
        content: 'Generated market insights for healthcare AI sector',
        timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString()
      },
      {
        id: 'event-3',
        type: 'agent_handoff',
        agentId: 'market-research',
        targetAgentId: 'content-strategy',
        content: 'Handed off market insights to content strategy agent',
        timestamp: new Date(Date.now() - 6 * 60 * 1000).toISOString()
      }
    ];

    setAgentActivities(mockAgents);
    setCollaborationEvents(mockEvents);
  };

  const simulateRealTimeUpdates = () => {
    // Simulate agent progress updates
    setAgentActivities(prev => prev.map(agent => {
      if (agent.status === 'analyzing' || agent.status === 'responding') {
        const newProgress = Math.min(agent.progress + Math.random() * 15, 100);
        const newStatus = newProgress >= 100 ? 'completed' : agent.status;
        
        // Occasionally add new insights
        const shouldAddInsight = Math.random() > 0.7;
        const newInsights = shouldAddInsight ? [
          ...agent.insights,
          {
            id: `insight-${Date.now()}`,
            content: generateRandomInsight(agent.agentId),
            confidence: 0.8 + Math.random() * 0.2,
            timestamp: new Date().toISOString()
          }
        ] : agent.insights;

        return {
          ...agent,
          progress: newProgress,
          status: newStatus,
          lastActivity: new Date().toISOString(),
          insights: newInsights.slice(-3) // Keep only last 3 insights
        };
      }
      return agent;
    }));

    // Occasionally add new collaboration events
    if (Math.random() > 0.6) {
      const newEvent: CollaborationEvent = {
        id: `event-${Date.now()}`,
        type: 'insight_generated',
        agentId: ['seo-keyword', 'content-strategy', 'market-research'][Math.floor(Math.random() * 3)],
        content: 'Generated new insight based on current analysis',
        timestamp: new Date().toISOString()
      };

      setCollaborationEvents(prev => [newEvent, ...prev.slice(0, 9)]); // Keep only last 10 events
    }
  };

  const generateRandomInsight = (agentId: string): string => {
    const insights = {
      'seo-keyword': [
        'Identified high-value long-tail keywords for better targeting',
        'Optimized keyword density for improved search rankings',
        'Found semantic keyword opportunities in related topics'
      ],
      'content-strategy': [
        'Recommended content structure improvements for better readability',
        'Suggested call-to-action placement for higher engagement',
        'Identified content gaps that should be addressed'
      ],
      'market-research': [
        'Discovered emerging trends in the target market',
        'Analyzed competitor content strategies and gaps',
        'Identified audience pain points for content focus'
      ]
    };

    const agentInsights = insights[agentId as keyof typeof insights] || ['Generated new insight'];
    return agentInsights[Math.floor(Math.random() * agentInsights.length)];
  };

  const getAgentDisplayName = (agentId: string) => {
    const names: Record<string, string> = {
      'seo-keyword': 'SEO & Keywords',
      'content-strategy': 'Content Strategy',
      'market-research': 'Market Research'
    };
    return names[agentId] || agentId.replace('-', ' ');
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'content-strategy': return '📋';
      case 'market-research': return '📊';
      default: return '🤖';
    }
  };

  const getStatusColor = (status: AgentActivity['status']) => {
    switch (status) {
      case 'analyzing': return 'text-blue-600 bg-blue-100';
      case 'responding': return 'text-green-600 bg-green-100';
      case 'completed': return 'text-gray-600 bg-gray-100';
      case 'idle': return 'text-yellow-600 bg-yellow-100';
    }
  };

  const getEventIcon = (type: CollaborationEvent['type']) => {
    switch (type) {
      case 'consultation_started': return <ArrowPathIcon className="h-4 w-4 text-blue-600" />;
      case 'insight_generated': return <LightBulbIcon className="h-4 w-4 text-yellow-600" />;
      case 'consultation_completed': return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case 'agent_handoff': return <UsersIcon className="h-4 w-4 text-purple-600" />;
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <UsersIcon className="h-6 w-6 mr-2" />
            Real-Time Agent Collaboration
          </h2>
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
              connectionStatus === 'connected' ? 'text-green-600 bg-green-100' :
              connectionStatus === 'connecting' ? 'text-yellow-600 bg-yellow-100' :
              'text-red-600 bg-red-100'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-600' :
                connectionStatus === 'connecting' ? 'bg-yellow-600 animate-pulse' :
                'bg-red-600'
              }`}></div>
              <span>{connectionStatus}</span>
            </div>
            <button
              onClick={() => setIsLive(!isLive)}
              className={`px-3 py-1 rounded text-sm font-medium ${
                isLive 
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {isLive ? 'Live' : 'Paused'}
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Active Agents */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Agents</h3>
            <div className="space-y-4">
              {agentActivities.map(agent => (
                <div key={agent.agentId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getAgentIcon(agent.agentId)}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">{getAgentDisplayName(agent.agentId)}</h4>
                        <p className="text-sm text-gray-600">{agent.currentTask}</p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                      {agent.status.toUpperCase()}
                    </span>
                  </div>
                  
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span className="text-gray-600">Progress</span>
                      <span className="font-medium">{Math.round(agent.progress)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          agent.status === 'completed' ? 'bg-green-600' :
                          agent.status === 'analyzing' ? 'bg-blue-600' :
                          agent.status === 'responding' ? 'bg-green-600' :
                          'bg-gray-400'
                        }`}
                        style={{ width: `${agent.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {agent.insights.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Latest Insights</h5>
                      <div className="space-y-2">
                        {agent.insights.slice(-2).map(insight => (
                          <div key={insight.id} className="bg-gray-50 rounded p-2">
                            <p className="text-sm text-gray-700">{insight.content}</p>
                            <div className="flex items-center justify-between mt-1">
                              <span className="text-xs text-gray-500">
                                {new Date(insight.timestamp).toLocaleTimeString()}
                              </span>
                              <span className="text-xs text-green-600 font-medium">
                                {Math.round(insight.confidence * 100)}% confidence
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Collaboration Timeline */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
              Collaboration Timeline
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {collaborationEvents.map(event => (
                <div key={event.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 mt-1">
                    {getEventIcon(event.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium text-gray-900">
                        {getAgentDisplayName(event.agentId)}
                      </span>
                      {event.targetAgentId && (
                        <>
                          <span className="text-gray-400">→</span>
                          <span className="text-sm font-medium text-gray-900">
                            {getAgentDisplayName(event.targetAgentId)}
                          </span>
                        </>
                      )}
                    </div>
                    <p className="text-sm text-gray-700">{event.content}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <ClockIcon className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
