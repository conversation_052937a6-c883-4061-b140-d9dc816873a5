'use client';

import React, { useState, useEffect } from 'react';
import { 
  UserGroupIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon 
} from '@heroicons/react/24/outline';

interface Reviewer {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'pending' | 'approved' | 'rejected' | 'unavailable';
  reviewedAt?: string;
  feedback?: string;
  priority: 'required' | 'optional';
}

interface ApprovalWorkflow {
  id: string;
  artifactId: string;
  title: string;
  description: string;
  requiredApprovals: number;
  currentApprovals: number;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  deadline?: string;
  createdAt: string;
  reviewers: Reviewer[];
  approvalType: 'unanimous' | 'majority' | 'any' | 'threshold';
  escalationRules?: {
    timeoutHours: number;
    escalateTo: string[];
    autoApprove: boolean;
  };
}

interface MultiReviewerApprovalSystemProps {
  artifactId?: string;
  className?: string;
  onApprovalComplete?: (approved: boolean, feedback: string[]) => void;
}

export default function MultiReviewerApprovalSystem({
  artifactId,
  className = '',
  onApprovalComplete
}: MultiReviewerApprovalSystemProps) {
  const [approvalWorkflow, setApprovalWorkflow] = useState<ApprovalWorkflow | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUserReview, setCurrentUserReview] = useState<{
    status: 'pending' | 'approved' | 'rejected';
    feedback: string;
  }>({ status: 'pending', feedback: '' });

  useEffect(() => {
    if (artifactId) {
      fetchApprovalWorkflow();
    } else {
      // Demo mode with mock data
      setApprovalWorkflow(generateMockApprovalWorkflow());
      setIsLoading(false);
    }
  }, [artifactId]);

  const fetchApprovalWorkflow = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/workflow/approval/${artifactId}/multi-reviewer`);
      const result = await response.json();
      
      if (result.success) {
        setApprovalWorkflow(result.data);
      } else {
        setApprovalWorkflow(generateMockApprovalWorkflow());
      }
    } catch (error) {
      console.error('Failed to fetch approval workflow:', error);
      setApprovalWorkflow(generateMockApprovalWorkflow());
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockApprovalWorkflow = (): ApprovalWorkflow => {
    return {
      id: 'approval-' + Date.now(),
      artifactId: artifactId || 'artifact-demo',
      title: 'Blog Post: "AI in Healthcare - Transforming Patient Care"',
      description: 'SEO-optimized blog post about AI applications in healthcare',
      requiredApprovals: 2,
      currentApprovals: 1,
      status: 'pending',
      deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      approvalType: 'majority',
      escalationRules: {
        timeoutHours: 24,
        escalateTo: ['<EMAIL>'],
        autoApprove: false
      },
      reviewers: [
        {
          id: 'reviewer-1',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          role: 'Content Manager',
          status: 'approved',
          reviewedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          feedback: 'Great content structure and SEO optimization. Minor grammar fixes needed.',
          priority: 'required'
        },
        {
          id: 'reviewer-2',
          name: 'Dr. Michael Chen',
          email: '<EMAIL>',
          role: 'Medical Expert',
          status: 'pending',
          priority: 'required'
        },
        {
          id: 'reviewer-3',
          name: 'Lisa Rodriguez',
          email: '<EMAIL>',
          role: 'SEO Specialist',
          status: 'pending',
          priority: 'optional'
        }
      ]
    };
  };

  const submitReview = async (approved: boolean, feedback: string) => {
    if (!approvalWorkflow) return;

    try {
      const response = await fetch(`/api/workflow/approval/${approvalWorkflow.artifactId}/review`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          approved,
          feedback,
          reviewerId: 'current-user' // This would be the actual user ID
        })
      });

      const result = await response.json();
      
      if (result.success) {
        // Update local state to reflect the review
        setCurrentUserReview({ 
          status: approved ? 'approved' : 'rejected', 
          feedback 
        });
        
        // Refresh the approval workflow
        await fetchApprovalWorkflow();
        
        if (onApprovalComplete) {
          onApprovalComplete(approved, [feedback]);
        }
      } else {
        alert('Failed to submit review: ' + result.error);
      }
    } catch (error) {
      console.error('Failed to submit review:', error);
      alert('Failed to submit review');
    }
  };

  const getStatusIcon = (status: Reviewer['status']) => {
    switch (status) {
      case 'approved': return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'rejected': return <XCircleIcon className="h-5 w-5 text-red-600" />;
      case 'pending': return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'unavailable': return <ExclamationTriangleIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: Reviewer['status']) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'unavailable': return 'text-gray-600 bg-gray-100';
    }
  };

  const getApprovalTypeDescription = (type: ApprovalWorkflow['approvalType']) => {
    switch (type) {
      case 'unanimous': return 'All reviewers must approve';
      case 'majority': return 'Majority of reviewers must approve';
      case 'any': return 'Any reviewer can approve';
      case 'threshold': return `At least ${approvalWorkflow?.requiredApprovals} approvals needed`;
    }
  };

  const getTimeRemaining = () => {
    if (!approvalWorkflow?.deadline) return null;
    const now = new Date();
    const deadline = new Date(approvalWorkflow.deadline);
    const diff = deadline.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h remaining`;
    }
    return `${hours}h ${minutes}m remaining`;
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading approval workflow...</span>
        </div>
      </div>
    );
  }

  if (!approvalWorkflow) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <UserGroupIcon className="h-8 w-8 mx-auto mb-2" />
          <p>No approval workflow found</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <UserGroupIcon className="h-6 w-6 mr-2" />
            Multi-Reviewer Approval
          </h2>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            approvalWorkflow.status === 'approved' ? 'text-green-600 bg-green-100' :
            approvalWorkflow.status === 'rejected' ? 'text-red-600 bg-red-100' :
            approvalWorkflow.status === 'expired' ? 'text-gray-600 bg-gray-100' :
            'text-yellow-600 bg-yellow-100'
          }`}>
            {approvalWorkflow.status.toUpperCase()}
          </span>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Approval Overview */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{approvalWorkflow.title}</h3>
          <p className="text-gray-600 mb-4">{approvalWorkflow.description}</p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-lg font-bold text-blue-600">
                {approvalWorkflow.currentApprovals} / {approvalWorkflow.requiredApprovals}
              </div>
              <div className="text-sm text-blue-800">Approvals</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3">
              <div className="text-sm font-medium text-purple-900">{getApprovalTypeDescription(approvalWorkflow.approvalType)}</div>
              <div className="text-xs text-purple-700">Approval Type</div>
            </div>
            <div className="bg-orange-50 rounded-lg p-3">
              <div className="text-sm font-medium text-orange-900">{getTimeRemaining()}</div>
              <div className="text-xs text-orange-700">Time Remaining</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm font-medium text-gray-900">{approvalWorkflow.reviewers.length}</div>
              <div className="text-xs text-gray-700">Total Reviewers</div>
            </div>
          </div>
        </div>

        {/* Reviewers List */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-3">Reviewers</h4>
          <div className="space-y-3">
            {approvalWorkflow.reviewers.map(reviewer => (
              <div key={reviewer.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(reviewer.status)}
                  <div>
                    <div className="font-medium text-gray-900">{reviewer.name}</div>
                    <div className="text-sm text-gray-600">{reviewer.role} • {reviewer.email}</div>
                    {reviewer.priority === 'required' && (
                      <span className="inline-block px-2 py-1 bg-red-100 text-red-800 text-xs rounded mt-1">
                        Required
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(reviewer.status)}`}>
                    {reviewer.status.toUpperCase()}
                  </span>
                  {reviewer.reviewedAt && (
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(reviewer.reviewedAt).toLocaleString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Feedback Section */}
        {approvalWorkflow.reviewers.some(r => r.feedback) && (
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
              Reviewer Feedback
            </h4>
            <div className="space-y-3">
              {approvalWorkflow.reviewers
                .filter(r => r.feedback)
                .map(reviewer => (
                  <div key={reviewer.id} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium text-gray-900">{reviewer.name}</span>
                      {getStatusIcon(reviewer.status)}
                    </div>
                    <p className="text-sm text-gray-700">{reviewer.feedback}</p>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Current User Review Form */}
        {currentUserReview.status === 'pending' && approvalWorkflow.status === 'pending' && (
          <div className="border-t border-gray-200 pt-6">
            <h4 className="text-md font-semibold text-gray-900 mb-3">Your Review</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Feedback (Optional)
                </label>
                <textarea
                  value={currentUserReview.feedback}
                  onChange={(e) => setCurrentUserReview(prev => ({ ...prev, feedback: e.target.value }))}
                  placeholder="Provide your feedback on this content..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => submitReview(true, currentUserReview.feedback)}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center space-x-2"
                >
                  <CheckCircleIcon className="h-4 w-4" />
                  <span>Approve</span>
                </button>
                <button
                  onClick={() => submitReview(false, currentUserReview.feedback)}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center justify-center space-x-2"
                >
                  <XCircleIcon className="h-4 w-4" />
                  <span>Reject</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Escalation Info */}
        {approvalWorkflow.escalationRules && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-900 mb-2">Escalation Rules</h4>
            <div className="text-sm text-yellow-800">
              <p>• Escalates after {approvalWorkflow.escalationRules.timeoutHours} hours</p>
              <p>• Escalates to: {approvalWorkflow.escalationRules.escalateTo.join(', ')}</p>
              {approvalWorkflow.escalationRules.autoApprove && (
                <p>• Auto-approves if no response after escalation</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
