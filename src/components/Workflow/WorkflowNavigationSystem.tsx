/**
 * Workflow Navigation System
 * Provides navigation, breadcrumbs, and status for workflow interfaces
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export interface NavigationState {
  currentStep: string;
  executionId?: string;
  reviewId?: string;
  workflowId?: string;
  status?: string;
  progress?: number;
}

interface WorkflowNavigationSystemProps {
  navigationState: NavigationState;
  workflowName: string;
  showBreadcrumbs?: boolean;
  showStatusBadge?: boolean;
  showActionButtons?: boolean;
  onBack?: () => void;
}

export function useWorkflowNavigation(currentStep: string, executionId?: string): NavigationState {
  return {
    currentStep,
    executionId,
    status: 'active',
    progress: getStepProgress(currentStep)
  };
}

function getStepProgress(step: string): number {
  const stepMap: Record<string, number> = {
    'template': 0,
    'configure': 20,
    'executing': 40,
    'collaboration': 60,
    'review': 80,
    'results': 100
  };
  return stepMap[step] || 0;
}

function getStepLabel(step: string): string {
  const stepLabels: Record<string, string> = {
    'template': 'Template Selection',
    'configure': 'Configuration',
    'executing': 'Execution',
    'collaboration': 'Agent Collaboration',
    'review': 'Human Review',
    'results': 'Results'
  };
  return stepLabels[step] || step;
}

function getStatusColor(status?: string): string {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800';
    case 'failed': return 'bg-red-100 text-red-800';
    case 'waiting_review': return 'bg-yellow-100 text-yellow-800';
    case 'running': return 'bg-blue-100 text-blue-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

export default function WorkflowNavigationSystem({
  navigationState,
  workflowName,
  showBreadcrumbs = true,
  showStatusBadge = true,
  showActionButtons = true,
  onBack
}: WorkflowNavigationSystemProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  const breadcrumbs = [
    { label: 'Workflows', href: '/workflow' },
    { label: 'Unified Experience', href: '/workflow/unified' },
    { label: getStepLabel(navigationState.currentStep), href: '#', current: true }
  ];

  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left side - Breadcrumbs and Title */}
          <div className="flex items-center space-x-4">
            {onBack && (
              <button
                onClick={onBack}
                className="text-gray-600 hover:text-gray-900 transition-colors"
                title="Go back"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
            )}
            
            <div>
              <h1 className="text-xl font-semibold text-gray-900">{workflowName}</h1>
              
              {showBreadcrumbs && (
                <nav className="flex" aria-label="Breadcrumb">
                  <ol className="flex items-center space-x-2 text-sm text-gray-500">
                    {breadcrumbs.map((crumb, index) => (
                      <li key={crumb.label} className="flex items-center">
                        {index > 0 && (
                          <svg className="w-4 h-4 mx-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                        {crumb.current ? (
                          <span className="font-medium text-gray-900">{crumb.label}</span>
                        ) : (
                          <Link href={crumb.href} className="hover:text-gray-700 transition-colors">
                            {crumb.label}
                          </Link>
                        )}
                      </li>
                    ))}
                  </ol>
                </nav>
              )}
            </div>
          </div>

          {/* Right side - Status and Actions */}
          <div className="flex items-center space-x-4">
            {/* Progress Indicator */}
            {navigationState.progress !== undefined && (
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${navigationState.progress}%` }}
                  />
                </div>
                <span className="text-sm text-gray-600">{navigationState.progress}%</span>
              </div>
            )}

            {/* Status Badge */}
            {showStatusBadge && navigationState.status && (
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(navigationState.status)}`}>
                {navigationState.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            )}

            {/* Execution ID */}
            {navigationState.executionId && (
              <div className="text-sm text-gray-500">
                ID: {navigationState.executionId.slice(-8)}
              </div>
            )}

            {/* Action Buttons */}
            {showActionButtons && (
              <div className="flex items-center space-x-2">
                <Link
                  href="/workflow/unified"
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                >
                  New Workflow
                </Link>
                <Link
                  href="/dashboard"
                  className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors"
                >
                  Dashboard
                </Link>
                <button
                  onClick={() => window.location.reload()}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  title="Refresh"
                >
                  🔄
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
