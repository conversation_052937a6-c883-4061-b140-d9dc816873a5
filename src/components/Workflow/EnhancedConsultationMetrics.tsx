'use client';

import React, { useState, useEffect } from 'react';
import { ChartBarIcon, ClockIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface AgentMetrics {
  totalConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  agentUtilization: Record<string, number>;
  consultationsByTrigger?: Record<string, number>;
  lastUpdated: string;
}

interface EnhancedConsultationMetricsProps {
  executionId?: string;
  className?: string;
  showTriggerAnalysis?: boolean;
  showAgentUtilization?: boolean;
}

export default function EnhancedConsultationMetrics({
  executionId,
  className = '',
  showTriggerAnalysis = true,
  showAgentUtilization = true
}: EnhancedConsultationMetricsProps) {
  const [metrics, setMetrics] = useState<AgentMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/agents/consultation?type=metrics');
      const result = await response.json();

      if (result.success) {
        // The API returns metrics nested in data.metrics
        const metricsData = result.data?.metrics || result.data;

        // Ensure agentUtilization is always an object
        if (metricsData && typeof metricsData === 'object') {
          setMetrics({
            ...metricsData,
            agentUtilization: metricsData.agentUtilization || {},
            consultationsByTrigger: metricsData.consultationsByTrigger || {}
          });
        } else {
          setError('Invalid metrics data received');
        }
      } else {
        setError(result.error || 'Failed to fetch metrics');
      }
    } catch (err) {
      console.error('Failed to fetch consultation metrics:', err);
      setError('Failed to fetch metrics');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    // Refresh metrics every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, [executionId]);

  const getTriggerTypeLabel = (triggerType: string) => {
    const labels: Record<string, string> = {
      'always': 'Always Triggered',
      'quality_threshold': 'Quality Threshold',
      'feedback_keywords': 'Feedback Keywords',
      'content_complexity': 'Content Complexity',
      'user_request': 'User Requested',
      'workflow-step': 'Workflow Step',
      'error_recovery': 'Error Recovery'
    };
    return labels[triggerType] || triggerType.replace('_', ' ').replace('-', ' ');
  };

  const getTriggerTypeIcon = (triggerType: string) => {
    switch (triggerType) {
      case 'always': return '🔄';
      case 'quality_threshold': return '📊';
      case 'feedback_keywords': return '💬';
      case 'content_complexity': return '🧠';
      case 'user_request': return '👤';
      case 'workflow-step': return '⚙️';
      case 'error_recovery': return '🔧';
      default: return '📋';
    }
  };

  const getAgentDisplayName = (agentId: string) => {
    const names: Record<string, string> = {
      'seo-keyword': 'SEO & Keywords',
      'content-strategy': 'Content Strategy',
      'market-research': 'Market Research'
    };
    return names[agentId] || agentId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'content-strategy': return '📋';
      case 'market-research': return '📊';
      default: return '🤖';
    }
  };

  if (isLoading && !metrics) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading metrics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-red-200 shadow-sm p-6 ${className}`}>
        <div className="flex items-center text-red-600">
          <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
          <span>Error loading metrics: {error}</span>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <ChartBarIcon className="h-8 w-8 mx-auto mb-2" />
          <p>No consultation metrics available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <ChartBarIcon className="h-5 w-5 mr-2" />
            Agent Consultation Metrics
          </h3>
          <button
            onClick={fetchMetrics}
            disabled={isLoading}
            className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
          >
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
        <p className="text-sm text-gray-500 mt-1">
          Last updated: {new Date(metrics.lastUpdated).toLocaleTimeString()}
        </p>
      </div>

      <div className="p-6 space-y-6">
        {/* Overview Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{metrics.totalConsultations}</div>
            <div className="text-sm text-blue-800">Total Consultations</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{Math.round(metrics.successRate * 100)}%</div>
            <div className="text-sm text-green-800">Success Rate</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-purple-600">{Math.round(metrics.averageConfidence * 100)}%</div>
            <div className="text-sm text-purple-800">Avg Confidence</div>
          </div>
          <div className="bg-orange-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-orange-600">{metrics.averageResponseTime}s</div>
            <div className="text-sm text-orange-800">Avg Response Time</div>
          </div>
        </div>

        {/* Trigger Analysis */}
        {showTriggerAnalysis && metrics.consultationsByTrigger && Object.keys(metrics.consultationsByTrigger).length > 0 && (
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <ClockIcon className="h-4 w-4 mr-2" />
              Consultation Triggers Analysis
            </h4>
            <div className="space-y-2">
              {Object.entries(metrics.consultationsByTrigger)
                .sort(([,a], [,b]) => b - a)
                .map(([triggerType, count]) => {
                  const percentage = metrics.totalConsultations > 0 
                    ? Math.round((count / metrics.totalConsultations) * 100) 
                    : 0;
                  
                  return (
                    <div key={triggerType} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getTriggerTypeIcon(triggerType)}</span>
                        <div>
                          <div className="font-medium text-gray-900">{getTriggerTypeLabel(triggerType)}</div>
                          <div className="text-sm text-gray-600">{count} consultations ({percentage}%)</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-700">{percentage}%</span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Agent Utilization */}
        {showAgentUtilization && metrics.agentUtilization && Object.keys(metrics.agentUtilization).length > 0 && (
          <div>
            <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Agent Utilization
            </h4>
            <div className="space-y-2">
              {Object.entries(metrics.agentUtilization)
                .sort(([,a], [,b]) => b - a)
                .map(([agentId, count]) => {
                  const percentage = metrics.totalConsultations > 0
                    ? Math.round((count / metrics.totalConsultations) * 100)
                    : 0;
                  
                  return (
                    <div key={agentId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getAgentIcon(agentId)}</span>
                        <div>
                          <div className="font-medium text-gray-900">{getAgentDisplayName(agentId)}</div>
                          <div className="text-sm text-gray-600">{count} consultations</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-700">{percentage}%</span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Performance Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-md font-semibold text-gray-900 mb-2">Performance Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Successful:</span>
              <span className="ml-2 font-medium text-green-600">{metrics.successfulConsultations}</span>
            </div>
            <div>
              <span className="text-gray-600">Failed:</span>
              <span className="ml-2 font-medium text-red-600">{metrics.failedConsultations}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
