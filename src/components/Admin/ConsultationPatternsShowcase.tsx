'use client';

import React, { useState, useEffect } from 'react';
import { SparklesIcon, CogIcon, ChartBarIcon, LightBulbIcon } from '@heroicons/react/24/outline';

interface ConsultationPattern {
  name: string;
  description: string;
  useCase: string;
  triggers: Array<{
    type: string;
    condition?: any;
    agents: string[];
    priority: string;
  }>;
  maxConsultations: number;
  timeoutMs: number;
  fallbackBehavior: string;
}

interface ConsultationPatternsShowcaseProps {
  className?: string;
}

export default function ConsultationPatternsShowcase({ className = '' }: ConsultationPatternsShowcaseProps) {
  const [selectedPattern, setSelectedPattern] = useState<string>('technical-content');
  const [patterns, setPatterns] = useState<Record<string, ConsultationPattern>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load consultation patterns from the backend
    const loadPatterns = async () => {
      try {
        // Simulate loading patterns from consultation-examples.ts
        const patternData = {
          'technical-content': {
            name: 'Technical Content Pattern',
            description: 'Optimized for technical documentation and complex content',
            useCase: 'Use for technical blogs, documentation, and complex tutorials',
            triggers: [
              {
                type: 'always',
                agents: ['content-strategy'],
                priority: 'high'
              },
              {
                type: 'content_complexity',
                condition: { threshold: 0.5 },
                agents: ['seo-keyword'],
                priority: 'medium'
              }
            ],
            maxConsultations: 2,
            timeoutMs: 30000,
            fallbackBehavior: 'continue'
          },
          'marketing-content': {
            name: 'Marketing Content Pattern',
            description: 'Designed for marketing materials and promotional content',
            useCase: 'Use for marketing campaigns, product descriptions, and promotional content',
            triggers: [
              {
                type: 'always',
                agents: ['seo-keyword', 'market-research'],
                priority: 'high'
              },
              {
                type: 'quality_threshold',
                condition: { threshold: 0.8 },
                agents: ['content-strategy'],
                priority: 'medium'
              }
            ],
            maxConsultations: 3,
            timeoutMs: 30000,
            fallbackBehavior: 'continue'
          },
          'seo-focused': {
            name: 'SEO-Focused Pattern',
            description: 'Heavily optimized for search engine optimization',
            useCase: 'Use for SEO-critical content and search-optimized articles',
            triggers: [
              {
                type: 'always',
                agents: ['seo-keyword'],
                priority: 'high'
              },
              {
                type: 'feedback_keywords',
                condition: { keywords: ['seo', 'keywords', 'optimization', 'ranking'] },
                agents: ['seo-keyword'],
                priority: 'high'
              },
              {
                type: 'quality_threshold',
                condition: { threshold: 0.7 },
                agents: ['market-research'],
                priority: 'medium'
              }
            ],
            maxConsultations: 3,
            timeoutMs: 30000,
            fallbackBehavior: 'continue'
          },
          'feedback-driven': {
            name: 'Feedback-Driven Pattern',
            description: 'Responsive to user feedback and iterative improvements',
            useCase: 'Use when expecting multiple rounds of feedback and revisions',
            triggers: [
              {
                type: 'feedback_keywords',
                condition: { 
                  keywords: [
                    'seo', 'keywords', 'optimization', 'ranking',
                    'market', 'audience', 'competitor',
                    'strategy', 'structure', 'content',
                    'improve', 'better', 'enhance'
                  ]
                },
                agents: ['seo-keyword', 'market-research', 'content-strategy'],
                priority: 'high'
              },
              {
                type: 'quality_threshold',
                condition: { threshold: 0.6 },
                agents: ['content-strategy'],
                priority: 'medium'
              }
            ],
            maxConsultations: 5,
            timeoutMs: 45000,
            fallbackBehavior: 'continue'
          }
        };
        
        setPatterns(patternData);
      } catch (error) {
        console.error('Failed to load consultation patterns:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPatterns();
  }, []);

  const getTriggerTypeIcon = (triggerType: string) => {
    switch (triggerType) {
      case 'always': return '🔄';
      case 'quality_threshold': return '📊';
      case 'feedback_keywords': return '💬';
      case 'content_complexity': return '🧠';
      case 'user_request': return '👤';
      default: return '📋';
    }
  };

  const getTriggerTypeLabel = (triggerType: string) => {
    const labels: Record<string, string> = {
      'always': 'Always Triggered',
      'quality_threshold': 'Quality Threshold',
      'feedback_keywords': 'Feedback Keywords',
      'content_complexity': 'Content Complexity',
      'user_request': 'User Requested'
    };
    return labels[triggerType] || triggerType.replace('_', ' ');
  };

  const getAgentDisplayName = (agentId: string) => {
    const names: Record<string, string> = {
      'seo-keyword': 'SEO & Keywords',
      'content-strategy': 'Content Strategy',
      'market-research': 'Market Research'
    };
    return names[agentId] || agentId.replace('-', ' ');
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'content-strategy': return '📋';
      case 'market-research': return '📊';
      default: return '🤖';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm p-6 ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading consultation patterns...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <SparklesIcon className="h-6 w-6 mr-2" />
          Consultation Patterns Showcase
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Explore different agent consultation patterns and their configurations
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Pattern Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Patterns</h3>
            <div className="space-y-2">
              {Object.entries(patterns).map(([key, pattern]) => (
                <button
                  key={key}
                  onClick={() => setSelectedPattern(key)}
                  className={`w-full text-left p-3 rounded-lg border transition-colors ${
                    selectedPattern === key 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium text-gray-900">{pattern.name}</div>
                  <div className="text-sm text-gray-600 mt-1">{pattern.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Pattern Details */}
          <div className="lg:col-span-2">
            {selectedPattern && patterns[selectedPattern] && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {patterns[selectedPattern].name}
                  </h3>
                  <p className="text-gray-600 mb-4">{patterns[selectedPattern].description}</p>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <LightBulbIcon className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">Use Case</span>
                    </div>
                    <p className="text-blue-800 text-sm">{patterns[selectedPattern].useCase}</p>
                  </div>
                </div>

                {/* Configuration Details */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
                    <CogIcon className="h-4 w-4 mr-2" />
                    Configuration
                  </h4>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm text-gray-600">Max Consultations</div>
                      <div className="font-semibold text-gray-900">{patterns[selectedPattern].maxConsultations}</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm text-gray-600">Timeout</div>
                      <div className="font-semibold text-gray-900">{patterns[selectedPattern].timeoutMs / 1000}s</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="text-sm text-gray-600">Fallback</div>
                      <div className="font-semibold text-gray-900 capitalize">{patterns[selectedPattern].fallbackBehavior}</div>
                    </div>
                  </div>
                </div>

                {/* Triggers */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center">
                    <ChartBarIcon className="h-4 w-4 mr-2" />
                    Consultation Triggers
                  </h4>
                  <div className="space-y-3">
                    {patterns[selectedPattern].triggers.map((trigger, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getTriggerTypeIcon(trigger.type)}</span>
                            <span className="font-medium text-gray-900">{getTriggerTypeLabel(trigger.type)}</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(trigger.priority)}`}>
                              {trigger.priority}
                            </span>
                          </div>
                        </div>
                        
                        {trigger.condition && (
                          <div className="mb-3 p-2 bg-gray-50 rounded text-sm">
                            <span className="font-medium">Condition: </span>
                            {JSON.stringify(trigger.condition)}
                          </div>
                        )}
                        
                        <div>
                          <span className="text-sm font-medium text-gray-700">Agents: </span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {trigger.agents.map(agentId => (
                              <span key={agentId} className="inline-flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                <span>{getAgentIcon(agentId)}</span>
                                <span>{getAgentDisplayName(agentId)}</span>
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
