'use client';

import React, { useState } from 'react';
import { PlayI<PERSON>, CheckCircleIcon, XCircleIcon, ClockIcon, BeakerIcon } from '@heroicons/react/24/outline';

interface TestResult {
  success: boolean;
  executionId?: string;
  finalStatus?: string;
  error?: string;
  message: string;
  metrics?: Record<string, any>;
}

interface WorkflowTestingInterfaceProps {
  className?: string;
}

export default function WorkflowTestingInterface({ className = '' }: WorkflowTestingInterfaceProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, TestResult>>({});
  const [selectedTest, setSelectedTest] = useState<string>('health');

  const testSuites = {
    health: {
      name: 'System Health Check',
      description: 'Verify basic system functionality and template availability',
      icon: '🏥'
    },
    basicExecution: {
      name: 'Basic Workflow Execution',
      description: 'Test workflow creation and execution with auto-approval',
      icon: '⚙️'
    },
    agentConsultation: {
      name: 'Agent Consultation Test',
      description: 'Verify agent consultation functionality and integration',
      icon: '🤖'
    },
    templateValidation: {
      name: 'Template Validation Test',
      description: 'Test template validation and execution order processing',
      icon: '📋'
    },
    comprehensive: {
      name: 'Comprehensive Test Suite',
      description: 'Run all tests including approval flows and consultation tests',
      icon: '🧪'
    }
  };

  const runTest = async (testType: string) => {
    setIsRunning(true);
    setTestResults(prev => ({ ...prev, [testType]: { success: false, message: 'Running...', metrics: {} } }));

    try {
      let response;
      
      switch (testType) {
        case 'health':
          response = await fetch('/api/workflow/test/health', { method: 'POST' });
          break;
        case 'basicExecution':
          response = await fetch('/api/workflow/test/execution', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              templateId: 'seo-blog-post',
              inputs: { topic: 'Test Blog Post', targetAudience: 'Test Audience' },
              options: { monitorDurationMs: 10000, autoApprove: true, approveAfterMs: 3000 }
            })
          });
          break;
        case 'agentConsultation':
          response = await fetch('/api/workflow/test/consultation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              templateId: 'seo-blog-post',
              inputs: { topic: 'AI and Machine Learning', targetAudience: 'Technical professionals' }
            })
          });
          break;
        case 'templateValidation':
          // Test template validation with a more comprehensive template
          response = await fetch('/api/workflow/templates', {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              template: {
                id: 'test-template',
                name: 'Test Template',
                workflow: {
                  id: 'test-workflow',
                  name: 'Test Workflow',
                  description: 'Test workflow for validation',
                  steps: [
                    {
                      id: 'step1',
                      name: 'Content Generation',
                      type: 'AI_GENERATION',
                      dependencies: [],
                      consultationConfig: {
                        enabled: true,
                        triggers: [{ type: 'always', agents: ['content-strategy'], priority: 'high' }],
                        maxConsultations: 2,
                        timeoutMs: 30000,
                        fallbackBehavior: 'continue'
                      }
                    },
                    {
                      id: 'step2',
                      name: 'Human Review',
                      type: 'HUMAN_REVIEW',
                      dependencies: ['step1']
                    }
                  ],
                  metadata: {
                    category: 'blog',
                    difficulty: 'beginner',
                    estimatedTime: '30 minutes'
                  }
                }
              }
            })
          });
          break;
        case 'comprehensive':
          response = await fetch('/api/workflow/test/suite', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              includeApprovalTests: true,
              includeConsultationTests: true,
              includeHealthTests: true
            })
          });
          break;
        default:
          throw new Error('Unknown test type');
      }

      const result = await response.json();
      
      if (result.success) {
        setTestResults(prev => ({ ...prev, [testType]: result.data || result }));
      } else {
        setTestResults(prev => ({ 
          ...prev, 
          [testType]: { 
            success: false, 
            error: result.error || 'Test failed',
            message: 'Test failed'
          }
        }));
      }
    } catch (error) {
      console.error(`Test ${testType} failed:`, error);
      setTestResults(prev => ({ 
        ...prev, 
        [testType]: { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error',
          message: 'Test failed'
        }
      }));
    } finally {
      setIsRunning(false);
    }
  };

  const getTestStatusIcon = (testType: string) => {
    const result = testResults[testType];
    if (!result) return <ClockIcon className="h-5 w-5 text-gray-400" />;
    if (result.message === 'Running...') return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600" />;
    if (result.success) return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
    return <XCircleIcon className="h-5 w-5 text-red-600" />;
  };

  const getTestStatusColor = (testType: string) => {
    const result = testResults[testType];
    if (!result) return 'text-gray-500';
    if (result.message === 'Running...') return 'text-blue-600';
    if (result.success) return 'text-green-600';
    return 'text-red-600';
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <BeakerIcon className="h-6 w-6 mr-2" />
          Workflow Testing Interface
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Test and validate workflow system functionality, agent consultation, and template processing
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Test Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Tests</h3>
            <div className="space-y-3">
              {Object.entries(testSuites).map(([key, suite]) => (
                <div
                  key={key}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedTest === key 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedTest(key)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{suite.icon}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">{suite.name}</h4>
                        <p className="text-sm text-gray-600">{suite.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getTestStatusIcon(key)}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          runTest(key);
                        }}
                        disabled={isRunning}
                        className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                      >
                        <PlayIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <button
                onClick={() => {
                  Object.keys(testSuites).forEach(testType => {
                    setTimeout(() => runTest(testType), Math.random() * 1000);
                  });
                }}
                disabled={isRunning}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center space-x-2"
              >
                <PlayIcon className="h-4 w-4" />
                <span>Run All Tests</span>
              </button>
            </div>
          </div>

          {/* Test Results */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
            
            {selectedTest && testResults[selectedTest] ? (
              <div className="space-y-4">
                <div className={`p-4 rounded-lg border ${
                  testResults[selectedTest].success 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-center space-x-2 mb-2">
                    {getTestStatusIcon(selectedTest)}
                    <h4 className={`font-medium ${getTestStatusColor(selectedTest)}`}>
                      {testSuites[selectedTest].name}
                    </h4>
                  </div>
                  <p className={`text-sm ${getTestStatusColor(selectedTest)}`}>
                    {testResults[selectedTest].message}
                  </p>
                  
                  {testResults[selectedTest].error && (
                    <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-sm text-red-700">
                      Error: {testResults[selectedTest].error}
                    </div>
                  )}

                  {testResults[selectedTest].metrics && Object.keys(testResults[selectedTest].metrics!).length > 0 && (
                    <div className="mt-3">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Metrics:</h5>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {Object.entries(testResults[selectedTest].metrics!).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-gray-600">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                            <span className="font-medium">{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {testResults[selectedTest].executionId && (
                    <div className="mt-2 text-xs text-gray-600">
                      Execution ID: {testResults[selectedTest].executionId}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BeakerIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>Select a test to view results</p>
                <p className="text-sm mt-1">Run tests to validate system functionality</p>
              </div>
            )}

            {/* Overall Status */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Overall Status</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">
                    {Object.values(testResults).filter(r => r.success).length}
                  </div>
                  <div className="text-gray-600">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">
                    {Object.values(testResults).filter(r => !r.success && r.message !== 'Running...').length}
                  </div>
                  <div className="text-gray-600">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {Object.values(testResults).filter(r => r.message === 'Running...').length}
                  </div>
                  <div className="text-gray-600">Running</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
