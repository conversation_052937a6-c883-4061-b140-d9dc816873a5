/**
 * Workflow Integration Test Utilities
 * 
 * Utilities for testing workflow execution, approval flows, and agent consultation
 * Extracted from deprecated test files and enhanced for current system
 */

import { getWorkflowEngine, getEnhancedTemplateRegistry } from '../singleton';
import { StepStatus, ExecutionStatus } from '../types';

export interface TestResult {
  success: boolean;
  executionId?: string;
  finalStatus?: string;
  error?: string;
  message: string;
  metrics?: Record<string, any>;
}

/**
 * Test workflow execution with approval gates
 */
export async function testWorkflowExecution(
  templateId: string,
  inputs: Record<string, any>,
  options: {
    userId?: string;
    monitorDurationMs?: number;
    autoApprove?: boolean;
    approveAfterMs?: number;
  } = {}
): Promise<TestResult> {
  const {
    userId = 'test-user',
    monitorDurationMs = 10000,
    autoApprove = false,
    approveAfterMs = 5000
  } = options;

  console.log(`🧪 Testing workflow execution with template: ${templateId}`);

  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();

    // Process template
    const processed = registry.processTemplate(templateId, userId);
    if (!processed) {
      throw new Error(`Failed to process template: ${templateId}`);
    }

    console.log(`✅ Template processed: ${processed.workflow.id}`);
    console.log(`📝 Approval gates: ${processed.approvalGates.length}`);

    // Create execution
    const execution = await engine.createExecution(
      processed.workflow,
      inputs,
      {
        userId,
        source: 'test',
        templateId
      }
    );

    console.log(`✅ Execution created: ${execution.id}`);

    // Register approval gates
    for (const gate of processed.approvalGates) {
      await engine.registerApprovalGate(gate);
      console.log(`✅ Registered approval gate: ${gate.id}`);
    }

    // Start execution
    console.log('▶️ Starting workflow execution...');
    await engine.executeWorkflow(execution.id);

    // Monitor execution
    const startTime = Date.now();
    let approved = false;

    while (Date.now() - startTime < monitorDurationMs) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const currentExecution = await engine.getExecution(execution.id);
      if (!currentExecution) {
        throw new Error('Execution not found');
      }

      console.log(`📊 Status: ${currentExecution.status}`);

      // Check for waiting approval steps
      const waitingSteps = Object.entries(currentExecution.stepResults)
        .filter(([_, result]) => result.status === StepStatus.WAITING_APPROVAL)
        .map(([stepId, result]) => ({ stepId, artifactId: result.artifactId }));

      if (waitingSteps.length > 0 && autoApprove && !approved) {
        const elapsed = Date.now() - startTime;
        if (elapsed >= approveAfterMs) {
          console.log('✅ Auto-approving first waiting step...');
          const firstWaiting = waitingSteps[0];
          if (firstWaiting.artifactId) {
            await engine.submitApproval(execution.id, firstWaiting.artifactId, {
              decision: 'approved',
              feedback: 'Auto-approved by test',
              reviewerId: userId
            });
            approved = true;
          }
        }
      }

      // Check completion
      if (currentExecution.status === ExecutionStatus.COMPLETED) {
        console.log('🎉 Workflow completed successfully!');
        break;
      } else if (currentExecution.status === ExecutionStatus.FAILED) {
        throw new Error(`Workflow failed: ${currentExecution.error}`);
      }
    }

    const finalExecution = await engine.getExecution(execution.id);
    const stepResults = Object.values(finalExecution?.stepResults || {});

    return {
      success: true,
      executionId: execution.id,
      finalStatus: finalExecution?.status,
      message: 'Workflow execution test completed',
      metrics: {
        totalSteps: stepResults.length,
        completedSteps: stepResults.filter(r => r.status === StepStatus.COMPLETED).length,
        waitingSteps: stepResults.filter(r => r.status === StepStatus.WAITING_APPROVAL).length,
        failedSteps: stepResults.filter(r => r.status === StepStatus.FAILED).length,
        approvalGates: processed.approvalGates.length
      }
    };

  } catch (error) {
    console.error('❌ Workflow execution test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Workflow execution test failed'
    };
  }
}

/**
 * Test agent consultation functionality
 */
export async function testAgentConsultation(
  templateId: string,
  inputs: Record<string, any>,
  expectedAgents: string[] = []
): Promise<TestResult> {
  console.log(`🤖 Testing agent consultation with template: ${templateId}`);

  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();

    const processed = registry.processTemplate(templateId, 'test-user');
    if (!processed) {
      throw new Error(`Failed to process template: ${templateId}`);
    }

    // Check if template has consultation config
    const hasConsultationSteps = processed.workflow.steps.some(
      step => step.consultationConfig?.enabled
    );

    if (!hasConsultationSteps) {
      return {
        success: false,
        message: 'Template does not have agent consultation enabled',
        error: 'No consultation configuration found'
      };
    }

    const execution = await engine.createExecution(
      processed.workflow,
      inputs,
      { userId: 'test-user', source: 'test' }
    );

    // Start execution and monitor for consultation activity
    await engine.executeWorkflow(execution.id);

    // Wait for potential consultations
    await new Promise(resolve => setTimeout(resolve, 5000));

    const finalExecution = await engine.getExecution(execution.id);
    
    // Check if consultations occurred (this would need to be enhanced based on actual implementation)
    const consultationMetrics = {
      hasConsultationConfig: hasConsultationSteps,
      executionCompleted: finalExecution?.status === ExecutionStatus.COMPLETED,
      stepsWithConsultation: processed.workflow.steps.filter(s => s.consultationConfig?.enabled).length
    };

    return {
      success: true,
      executionId: execution.id,
      finalStatus: finalExecution?.status,
      message: 'Agent consultation test completed',
      metrics: consultationMetrics
    };

  } catch (error) {
    console.error('❌ Agent consultation test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Agent consultation test failed'
    };
  }
}

/**
 * Test system health and performance
 */
export async function testSystemHealth(): Promise<TestResult> {
  console.log('🏥 Testing system health...');

  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();

    // Test basic functionality
    const templates = registry.getAllTemplates();
    if (templates.length === 0) {
      throw new Error('No templates available');
    }

    // Test template processing
    const firstTemplate = templates[0];
    const processed = registry.processTemplate(firstTemplate.id, 'test-user');
    if (!processed) {
      throw new Error('Template processing failed');
    }

    // Test execution creation (without running)
    const testExecution = await engine.createExecution(
      processed.workflow,
      { topic: 'Health Check Test' },
      { userId: 'test-user', source: 'health-check' }
    );

    const healthMetrics = {
      templatesAvailable: templates.length,
      templateProcessingWorking: !!processed,
      executionCreationWorking: !!testExecution,
      approvalGatesConfigured: processed.approvalGates.length,
      consultationStepsConfigured: processed.workflow.steps.filter(s => s.consultationConfig?.enabled).length
    };

    return {
      success: true,
      message: 'System health check passed',
      metrics: healthMetrics
    };

  } catch (error) {
    console.error('❌ System health check failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'System health check failed'
    };
  }
}

/**
 * Comprehensive test suite
 */
export async function runTestSuite(options: {
  includeApprovalTests?: boolean;
  includeConsultationTests?: boolean;
  includeHealthTests?: boolean;
} = {}): Promise<{
  overall: boolean;
  results: Record<string, TestResult>;
}> {
  const {
    includeApprovalTests = true,
    includeConsultationTests = true,
    includeHealthTests = true
  } = options;

  console.log('🧪 Running comprehensive test suite...');

  const results: Record<string, TestResult> = {};

  // Health check
  if (includeHealthTests) {
    results.health = await testSystemHealth();
  }

  // Basic workflow execution
  if (includeApprovalTests) {
    results.basicExecution = await testWorkflowExecution(
      'seo-blog-post',
      {
        topic: 'Test Blog Post',
        targetAudience: 'Test Audience'
      },
      { monitorDurationMs: 5000 }
    );
  }

  // Agent consultation
  if (includeConsultationTests) {
    results.agentConsultation = await testAgentConsultation(
      'seo-blog-post',
      {
        topic: 'AI and Machine Learning',
        targetAudience: 'Technical professionals'
      }
    );
  }

  const overall = Object.values(results).every(result => result.success);

  console.log(`🏁 Test suite completed. Overall success: ${overall}`);

  return { overall, results };
}

export const WorkflowTestUtils = {
  testWorkflowExecution,
  testAgentConsultation,
  testSystemHealth,
  runTestSuite
};
