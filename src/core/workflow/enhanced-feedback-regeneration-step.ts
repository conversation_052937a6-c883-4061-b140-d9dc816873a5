/**
 * Enhanced Feedback-Driven Regeneration Step
 * 
 * Integrates the Smart Feedback Processing Agent to intelligently
 * handle user feedback and regenerate content with agent consultation.
 */

import { WorkflowStep, StepType } from './types';
import { SmartFeedbackProcessingAgent, FeedbackAnalysis, RegenerationPlan } from '../agents/feedback-processing-agent';
import { DynamicAgentConsultationService } from './dynamic-agent-consultation-service';
import { EnhancedAIGenerationStep } from './enhanced-ai-generation-step';

export interface FeedbackRegenerationConfig {
  maxRegenerationAttempts: number;
  qualityThreshold: number;
  timeoutMs: number;
  enableSmartAnalysis: boolean;
  fallbackBehavior: 'continue' | 'fail' | 'manual_review';
}

export interface FeedbackRegenerationResult {
  success: boolean;
  regeneratedContent: any;
  feedbackAnalysis: FeedbackAnalysis;
  regenerationPlan: RegenerationPlan;
  agentConsultationResults: any[];
  qualityScore: number;
  improvementMetrics: {
    contentQualityImprovement: number;
    seoScoreImprovement: number;
    structureImprovement: number;
    userSatisfactionPrediction: number;
  };
  metadata: {
    isRegeneration: boolean;
    version: string;
    userFeedbackIncorporated: boolean;
    regenerationAttempts: number;
    feedbackProcessingTime: number;
    totalRegenerationTime: number;
    agentInsightsApplied: number;
    enhancedWithAgents: boolean;
  };
}

export class EnhancedFeedbackRegenerationStep {
  private feedbackAgent: SmartFeedbackProcessingAgent;
  private consultationService: DynamicAgentConsultationService;
  private aiGenerationStep: EnhancedAIGenerationStep;

  public readonly id: string;
  public readonly name: string;
  public readonly type: StepType = StepType.AI_GENERATION;

  constructor(
    id: string,
    name: string,
    private config: FeedbackRegenerationConfig
  ) {
    this.id = id;
    this.name = name;
    this.feedbackAgent = new SmartFeedbackProcessingAgent();
    // Initialize consultation service with proper agent bridge
    this.consultationService = new DynamicAgentConsultationService();
    this.aiGenerationStep = new EnhancedAIGenerationStep(
      `${id}_ai_generation`,
      'AI Content Regeneration',
      {
        model: 'gpt-4',
        maxTokens: 4000,
        temperature: 0.7,
        enableAgentConsultation: true,
        consultationConfig: {
          enabled: true,
          triggers: [
            {
              type: 'always',
              agents: ['seo-keyword', 'content-strategy', 'market-research'],
              priority: 'high'
            }
          ],
          maxConsultations: 5,
          timeoutMs: 30000,
          fallbackBehavior: 'continue'
        }
      }
    );
  }

  async execute(context: Record<string, any>): Promise<FeedbackRegenerationResult> {
    const startTime = Date.now();
    console.log(`🔄 Starting enhanced feedback-driven regeneration for execution: ${context.executionId}`);

    try {
      // Extract required data from context
      const {
        executionId,
        artifactId,
        userFeedback,
        originalContent,
        previousAttempts = 0
      } = context;

      if (!userFeedback || !originalContent) {
        throw new Error('Missing required feedback or original content');
      }

      // Step 1: Smart feedback analysis
      console.log('🧠 Analyzing user feedback with Smart Feedback Agent...');
      const feedbackProcessingStart = Date.now();
      
      const { analysis, regenerationPlan, enhancedPrompts } = await this.feedbackAgent.processFeedbackAndRegenerate(
        executionId,
        artifactId,
        userFeedback,
        originalContent,
        context
      );

      const feedbackProcessingTime = Date.now() - feedbackProcessingStart;

      // Step 2: Agent consultation based on feedback analysis
      console.log(`🤖 Consulting ${analysis.recommendedAgents.length} agents based on feedback analysis...`);
      const consultationResults = await this.consultAgentsForRegeneration(
        executionId,
        analysis,
        enhancedPrompts,
        context
      );

      // Step 3: Enhanced content regeneration
      console.log('✨ Regenerating content with agent insights...');
      const regenerationContext = {
        ...context,
        feedbackAnalysis: analysis,
        agentConsultationResults: consultationResults,
        enhancedPrompts,
        regenerationStrategy: regenerationPlan.strategy,
        isRegeneration: true,
        version: `v${previousAttempts + 2}`,
        userFeedbackIncorporated: true
      };

      const regenerationResult = await this.aiGenerationStep.execute(regenerationContext);

      // Step 4: Quality assessment and improvement metrics
      const qualityMetrics = this.calculateImprovementMetrics(
        originalContent,
        regenerationResult.content,
        analysis,
        consultationResults
      );

      // Step 5: Prepare enhanced result with metadata
      const totalRegenerationTime = Date.now() - startTime;
      
      const result: FeedbackRegenerationResult = {
        success: true,
        regeneratedContent: regenerationResult.content,
        feedbackAnalysis: analysis,
        regenerationPlan,
        agentConsultationResults: consultationResults,
        qualityScore: qualityMetrics.overallQualityScore,
        improvementMetrics: {
          contentQualityImprovement: qualityMetrics.contentQualityImprovement,
          seoScoreImprovement: qualityMetrics.seoScoreImprovement,
          structureImprovement: qualityMetrics.structureImprovement,
          userSatisfactionPrediction: qualityMetrics.userSatisfactionPrediction
        },
        metadata: {
          isRegeneration: true,
          version: `v${previousAttempts + 2}`,
          userFeedbackIncorporated: true,
          regenerationAttempts: previousAttempts + 1,
          feedbackProcessingTime,
          totalRegenerationTime,
          agentInsightsApplied: consultationResults.length,
          enhancedWithAgents: consultationResults.length > 0
        }
      };

      console.log('✅ Enhanced feedback-driven regeneration completed:', {
        qualityScore: Math.round(result.qualityScore * 100),
        improvementMetrics: result.improvementMetrics,
        agentsConsulted: consultationResults.length,
        totalTime: `${totalRegenerationTime}ms`
      });

      return result;

    } catch (error) {
      console.error('❌ Enhanced feedback regeneration failed:', error);
      
      return {
        success: false,
        regeneratedContent: null,
        feedbackAnalysis: {} as FeedbackAnalysis,
        regenerationPlan: {} as RegenerationPlan,
        agentConsultationResults: [],
        qualityScore: 0,
        improvementMetrics: {
          contentQualityImprovement: 0,
          seoScoreImprovement: 0,
          structureImprovement: 0,
          userSatisfactionPrediction: 0
        },
        metadata: {
          isRegeneration: true,
          version: `v${context.previousAttempts + 2}`,
          userFeedbackIncorporated: false,
          regenerationAttempts: context.previousAttempts + 1,
          feedbackProcessingTime: 0,
          totalRegenerationTime: Date.now() - startTime,
          agentInsightsApplied: 0,
          enhancedWithAgents: false
        }
      };
    }
  }

  private async consultAgentsForRegeneration(
    executionId: string,
    analysis: FeedbackAnalysis,
    enhancedPrompts: Record<string, string>,
    context: Record<string, any>
  ): Promise<any[]> {
    const consultationResults = [];

    for (const agentId of analysis.recommendedAgents) {
      try {
        console.log(`🤖 Consulting ${agentId} agent for regeneration insights...`);
        
        const agentPrompt = enhancedPrompts[agentId] || enhancedPrompts.base;
        const consultationContext = {
          ...context,
          feedbackAnalysis: analysis,
          regenerationPrompt: agentPrompt,
          focusAreas: analysis.regenerationStrategy.focusAreas
        };

        const result = await this.consultationService.consultAgent(
          agentId as any,
          executionId,
          `feedback_regeneration_${agentId}`,
          consultationContext
        );

        if (result) {
          consultationResults.push(result);
        }

      } catch (error) {
        console.error(`❌ Failed to consult ${agentId} agent:`, error);
        // Continue with other agents
      }
    }

    return consultationResults;
  }

  private calculateImprovementMetrics(
    originalContent: any,
    regeneratedContent: any,
    analysis: FeedbackAnalysis,
    consultationResults: any[]
  ): any {
    // Calculate various improvement metrics
    const originalWordCount = this.getWordCount(originalContent);
    const regeneratedWordCount = this.getWordCount(regeneratedContent);
    
    const contentQualityImprovement = this.assessContentQualityImprovement(
      originalContent,
      regeneratedContent,
      analysis
    );
    
    const seoScoreImprovement = this.assessSEOImprovement(
      originalContent,
      regeneratedContent,
      consultationResults
    );
    
    const structureImprovement = this.assessStructureImprovement(
      originalContent,
      regeneratedContent,
      analysis
    );
    
    const userSatisfactionPrediction = this.predictUserSatisfaction(
      analysis,
      consultationResults,
      contentQualityImprovement
    );

    const overallQualityScore = (
      contentQualityImprovement * 0.4 +
      seoScoreImprovement * 0.3 +
      structureImprovement * 0.2 +
      userSatisfactionPrediction * 0.1
    );

    return {
      overallQualityScore,
      contentQualityImprovement,
      seoScoreImprovement,
      structureImprovement,
      userSatisfactionPrediction
    };
  }

  private getWordCount(content: any): number {
    const text = typeof content === 'string' ? content : content?.content || '';
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  private assessContentQualityImprovement(original: any, regenerated: any, analysis: FeedbackAnalysis): number {
    // Simple heuristic based on feedback type and content changes
    let improvement = 0.7; // Base improvement score
    
    if (analysis.severity === 'major') improvement += 0.2;
    if (analysis.confidence > 0.8) improvement += 0.1;
    
    return Math.min(0.95, improvement);
  }

  private assessSEOImprovement(original: any, regenerated: any, consultationResults: any[]): number {
    const seoConsultations = consultationResults.filter(r => r.agentId === 'seo-keyword');
    return seoConsultations.length > 0 ? 0.85 : 0.7;
  }

  private assessStructureImprovement(original: any, regenerated: any, analysis: FeedbackAnalysis): number {
    const hasStructureFeedback = analysis.categories.some(cat => cat.category === 'structure');
    return hasStructureFeedback ? 0.9 : 0.75;
  }

  private predictUserSatisfaction(analysis: FeedbackAnalysis, consultationResults: any[], qualityImprovement: number): number {
    // Predict user satisfaction based on feedback analysis and improvements
    let satisfaction = qualityImprovement * 0.8;
    
    if (analysis.confidence > 0.8) satisfaction += 0.1;
    if (consultationResults.length >= 2) satisfaction += 0.1;
    
    return Math.min(0.95, satisfaction);
  }
}
