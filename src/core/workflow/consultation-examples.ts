/**
 * Agent Consultation Examples and Utilities
 * 
 * Provides example configurations and utility functions for agent consultation
 * in workflows. Extracted from deprecated integration examples.
 */

import { AgentConsultationConfig } from './types';

/**
 * Create custom consultation configuration for different use cases
 */
export function createConsultationConfig(
  useCase: 'technical-content' | 'marketing-content' | 'general-content' | 'seo-focused'
): AgentConsultationConfig {
  const baseConfig: AgentConsultationConfig = {
    enabled: true,
    triggers: [],
    maxConsultations: 3,
    timeoutMs: 30000,
    fallbackBehavior: 'continue'
  };

  switch (useCase) {
    case 'technical-content':
      baseConfig.triggers = [
        {
          type: 'always',
          agents: ['content-strategy'],
          priority: 'high'
        },
        {
          type: 'content_complexity',
          condition: { threshold: 0.5 },
          agents: ['seo-keyword'],
          priority: 'medium'
        }
      ];
      baseConfig.maxConsultations = 2;
      break;

    case 'marketing-content':
      baseConfig.triggers = [
        {
          type: 'always',
          agents: ['seo-keyword', 'market-research'],
          priority: 'high'
        },
        {
          type: 'quality_threshold',
          condition: { threshold: 0.8 },
          agents: ['content-strategy'],
          priority: 'medium'
        }
      ];
      break;

    case 'seo-focused':
      baseConfig.triggers = [
        {
          type: 'always',
          agents: ['seo-keyword'],
          priority: 'high'
        },
        {
          type: 'feedback_keywords',
          condition: { keywords: ['seo', 'keywords', 'optimization', 'ranking'] },
          agents: ['seo-keyword'],
          priority: 'high'
        },
        {
          type: 'quality_threshold',
          condition: { threshold: 0.7 },
          agents: ['market-research'],
          priority: 'medium'
        }
      ];
      break;

    case 'general-content':
      baseConfig.triggers = [
        {
          type: 'feedback_keywords',
          condition: { keywords: ['improve', 'better', 'enhance'] },
          agents: ['content-strategy'],
          priority: 'medium'
        },
        {
          type: 'quality_threshold',
          condition: { threshold: 0.7 },
          agents: ['seo-keyword', 'market-research'],
          priority: 'low'
        }
      ];
      break;
  }

  return baseConfig;
}

/**
 * Enhanced consultation configuration for blog posts
 */
export function createBlogPostConsultationConfig(): AgentConsultationConfig {
  return {
    enabled: true,
    triggers: [
      // Always consult SEO and market research agents for blog posts
      {
        type: 'always',
        agents: ['seo-keyword', 'market-research'],
        priority: 'high'
      },
      // Consult content strategy agent for complex topics
      {
        type: 'content_complexity',
        condition: { threshold: 0.6 },
        agents: ['content-strategy'],
        priority: 'medium'
      },
      // Consult SEO agent when feedback mentions SEO issues
      {
        type: 'feedback_keywords',
        condition: { keywords: ['seo', 'keywords', 'optimization', 'ranking'] },
        agents: ['seo-keyword'],
        priority: 'high'
      },
      // Consult strategy agent when quality is below threshold
      {
        type: 'quality_threshold',
        condition: { threshold: 0.8 },
        agents: ['content-strategy'],
        priority: 'medium'
      }
    ],
    maxConsultations: 3,
    timeoutMs: 30000,
    fallbackBehavior: 'continue'
  };
}

/**
 * Consultation configuration for feedback-driven improvements
 */
export function createFeedbackDrivenConfig(): AgentConsultationConfig {
  return {
    enabled: true,
    triggers: [
      {
        type: 'feedback_keywords',
        condition: { 
          keywords: [
            'seo', 'keywords', 'optimization', 'ranking',
            'market', 'audience', 'competitor',
            'strategy', 'structure', 'content',
            'improve', 'better', 'enhance'
          ]
        },
        agents: ['seo-keyword', 'market-research', 'content-strategy'],
        priority: 'high'
      },
      {
        type: 'quality_threshold',
        condition: { threshold: 0.6 },
        agents: ['content-strategy'],
        priority: 'medium'
      }
    ],
    maxConsultations: 5,
    timeoutMs: 45000,
    fallbackBehavior: 'continue'
  };
}

/**
 * Generate health monitoring recommendations based on metrics
 */
export function generateHealthRecommendations(metrics: {
  successRate: number;
  averageResponseTime: number;
  totalConsultations: number;
  agentUtilization: Record<string, number>;
}): string[] {
  const recommendations: string[] = [];

  if (metrics.successRate < 0.9) {
    recommendations.push('Consultation success rate is below 90% - consider increasing timeouts or checking agent availability');
  }

  if (metrics.averageResponseTime > 10000) {
    recommendations.push('Average response time is high (>10s) - consider optimizing agent performance or reducing consultation complexity');
  }

  if (metrics.totalConsultations === 0) {
    recommendations.push('No consultations have been performed - verify agent registration and trigger configuration');
  }

  // Check for uneven agent utilization
  const utilizationValues = Object.values(metrics.agentUtilization);
  if (utilizationValues.length > 1) {
    const max = Math.max(...utilizationValues);
    const min = Math.min(...utilizationValues);
    if (max > min * 3) {
      recommendations.push('Uneven agent utilization detected - consider balancing consultation triggers');
    }
  }

  if (recommendations.length === 0) {
    recommendations.push('System health looks good - all metrics are within acceptable ranges');
  }

  return recommendations;
}

/**
 * Common consultation trigger patterns
 */
export const CONSULTATION_PATTERNS = {
  ALWAYS_SEO: {
    type: 'always' as const,
    agents: ['seo-keyword' as const],
    priority: 'high' as const
  },
  
  QUALITY_FALLBACK: {
    type: 'quality_threshold' as const,
    condition: { threshold: 0.7 },
    agents: ['content-strategy' as const],
    priority: 'medium' as const
  },
  
  FEEDBACK_RESPONSIVE: {
    type: 'feedback_keywords' as const,
    condition: { keywords: ['improve', 'better', 'enhance', 'fix'] },
    agents: ['content-strategy' as const, 'seo-keyword' as const],
    priority: 'high' as const
  },
  
  COMPLEXITY_AWARE: {
    type: 'content_complexity' as const,
    condition: { threshold: 0.6 },
    agents: ['content-strategy' as const],
    priority: 'medium' as const
  }
};

/**
 * Export utility functions for easy access
 */
export const ConsultationUtils = {
  createConsultationConfig,
  createBlogPostConsultationConfig,
  createFeedbackDrivenConfig,
  generateHealthRecommendations,
  CONSULTATION_PATTERNS
};
