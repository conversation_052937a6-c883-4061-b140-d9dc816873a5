/**
 * Test to verify agents generate real, contextually relevant data
 * instead of dummy/mock data
 */

import { SeoKeywordAgent } from '../seo-keyword-agent';
import { MarketResearchAgent } from '../market-research-agent';
import { ContentStrategyAgent } from '../content-strategy-agent';
import { AgentConsultationRequest } from '../types';

describe('Real Agent Data Generation', () => {
  let seoAgent: SeoKeywordAgent;
  let marketAgent: MarketResearchAgent;
  let strategyAgent: ContentStrategyAgent;

  beforeEach(() => {
    seoAgent = new SeoKeywordAgent();
    marketAgent = new MarketResearchAgent();
    strategyAgent = new ContentStrategyAgent();
  });

  describe('SEO Keyword Agent', () => {
    it('should generate contextually relevant keywords for AI tools topic', async () => {
      const request: AgentConsultationRequest = {
        id: 'test-1',
        workflowExecutionId: 'test-workflow',
        stepId: 'test-step',
        agentId: 'seo-keyword',
        question: 'What are the best keywords for AI tools for developers?',
        context: {
          topic: 'ai tools for developers',
          targetAudience: 'software developers',
          contentType: 'blog-post'
        },
        priority: 'high',
        timeoutMs: 30000,
        createdAt: new Date().toISOString()
      };

      const response = await seoAgent.processConsultation(request);

      // Verify response structure
      expect(response).toBeDefined();
      expect(response.agentId).toBe('seo-keyword');
      expect(response.confidence).toBeGreaterThan(0);
      expect(response.processingTime).toBeGreaterThan(0);

      // Verify real keyword data
      expect(response.response.keywordAnalysis).toBeDefined();
      expect(response.response.keywordAnalysis.primaryKeywords).toBeInstanceOf(Array);
      expect(response.response.keywordAnalysis.primaryKeywords.length).toBeGreaterThan(0);
      
      // Check that keywords are contextually relevant to the topic
      const keywords = response.response.keywordAnalysis.primaryKeywords;
      const topicRelevant = keywords.some(keyword => 
        keyword.toLowerCase().includes('ai') || 
        keyword.toLowerCase().includes('tools') || 
        keyword.toLowerCase().includes('developer')
      );
      expect(topicRelevant).toBe(true);

      // Verify search volume and difficulty are realistic numbers, not random
      const searchVolume = response.response.keywordAnalysis.searchVolume;
      const keywordDifficulty = response.response.keywordAnalysis.keywordDifficulty;
      
      expect(searchVolume).toBeDefined();
      expect(keywordDifficulty).toBeDefined();
      
      // Check that values are within realistic ranges
      Object.values(searchVolume).forEach(volume => {
        expect(volume).toBeGreaterThan(0);
        expect(volume).toBeLessThan(100000); // Reasonable upper bound
      });

      Object.values(keywordDifficulty).forEach(difficulty => {
        expect(difficulty).toBeGreaterThanOrEqual(15);
        expect(difficulty).toBeLessThanOrEqual(85);
      });
    });

    it('should generate different keywords for different topics', async () => {
      const request1: AgentConsultationRequest = {
        id: 'test-2a',
        workflowExecutionId: 'test-workflow',
        stepId: 'test-step',
        agentId: 'seo-keyword',
        question: 'Keywords for AI tools',
        context: { topic: 'ai tools for developers', targetAudience: 'developers', contentType: 'blog-post' },
        priority: 'high',
        timeoutMs: 30000,
        createdAt: new Date().toISOString()
      };

      const request2: AgentConsultationRequest = {
        id: 'test-2b',
        workflowExecutionId: 'test-workflow',
        stepId: 'test-step',
        agentId: 'seo-keyword',
        question: 'Keywords for marketing automation',
        context: { topic: 'marketing automation tools', targetAudience: 'marketers', contentType: 'blog-post' },
        priority: 'high',
        timeoutMs: 30000,
        createdAt: new Date().toISOString()
      };

      const response1 = await seoAgent.processConsultation(request1);
      const response2 = await seoAgent.processConsultation(request2);

      const keywords1 = response1.response.keywordAnalysis.primaryKeywords;
      const keywords2 = response2.response.keywordAnalysis.primaryKeywords;

      // Keywords should be different for different topics
      const overlap = keywords1.filter(k1 => keywords2.includes(k1));
      expect(overlap.length).toBeLessThan(keywords1.length); // Should have some unique keywords
    });
  });

  describe('Market Research Agent', () => {
    it('should generate realistic market size estimates', async () => {
      const request: AgentConsultationRequest = {
        id: 'test-3',
        workflowExecutionId: 'test-workflow',
        stepId: 'test-step',
        agentId: 'market-research',
        question: 'What is the market size for AI tools?',
        context: {
          topic: 'ai tools for developers',
          targetAudience: 'software developers',
          industry: 'technology'
        },
        priority: 'high',
        timeoutMs: 30000,
        createdAt: new Date().toISOString()
      };

      const response = await marketAgent.processConsultation(request);

      expect(response).toBeDefined();
      expect(response.agentId).toBe('market-research');
      expect(response.response.marketAnalysis).toBeDefined();
      
      const marketSize = response.response.marketAnalysis.marketSize;
      expect(marketSize).toBeDefined();
      expect(typeof marketSize).toBe('string');
      
      // Should contain realistic market size indicators
      expect(marketSize).toMatch(/\$[\d.]+[BM]/); // Should have dollar amount with B or M
      expect(marketSize.toLowerCase()).toMatch(/growing|annually/); // Should mention growth
    });

    it('should provide contextually relevant competitor analysis', async () => {
      const request: AgentConsultationRequest = {
        id: 'test-4',
        workflowExecutionId: 'test-workflow',
        stepId: 'test-step',
        agentId: 'market-research',
        question: 'Who are the competitors in AI tools space?',
        context: {
          topic: 'ai tools for developers',
          targetAudience: 'software developers',
          industry: 'technology'
        },
        priority: 'high',
        timeoutMs: 30000,
        createdAt: new Date().toISOString()
      };

      const response = await marketAgent.processConsultation(request);
      
      const competitorAnalysis = response.response.marketAnalysis.competitorAnalysis;
      expect(competitorAnalysis).toBeDefined();
      expect(competitorAnalysis).toBeInstanceOf(Array);
      expect(competitorAnalysis.length).toBeGreaterThan(0);

      // Each competitor should have realistic structure
      competitorAnalysis.forEach(competitor => {
        expect(competitor.name).toBeDefined();
        expect(competitor.strengths).toBeInstanceOf(Array);
        expect(competitor.weaknesses).toBeInstanceOf(Array);
        expect(competitor.strengths.length).toBeGreaterThan(0);
        expect(competitor.weaknesses.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Content Strategy Agent', () => {
    it('should generate contextually appropriate content structure', async () => {
      const request: AgentConsultationRequest = {
        id: 'test-5',
        workflowExecutionId: 'test-workflow',
        stepId: 'test-step',
        agentId: 'content-strategy',
        question: 'What should be the content structure for AI tools article?',
        context: {
          topic: 'ai tools for developers',
          targetAudience: 'software developers',
          contentType: 'blog-post',
          goals: ['educate', 'generate leads']
        },
        priority: 'high',
        timeoutMs: 30000,
        createdAt: new Date().toISOString()
      };

      const response = await strategyAgent.processConsultation(request);

      expect(response).toBeDefined();
      expect(response.agentId).toBe('content-strategy');
      expect(response.response.strategyAnalysis).toBeDefined();
      expect(response.response.strategyAnalysis.contentOutline).toBeDefined();

      const contentOutline = response.response.strategyAnalysis.contentOutline;
      expect(contentOutline).toBeInstanceOf(Array);
      expect(contentOutline.length).toBeGreaterThan(0);

      // Each section should have proper structure
      contentOutline.forEach(section => {
        expect(section.section).toBeDefined();
        expect(section.purpose).toBeDefined();
        expect(section.keyPoints).toBeInstanceOf(Array);
        expect(section.keyPoints.length).toBeGreaterThan(0);
      });

      // Should include Introduction and Conclusion
      const sectionNames = contentOutline.map(s => s.section.toLowerCase());
      expect(sectionNames).toContain('introduction');
      expect(sectionNames).toContain('conclusion');
    });
  });
});
