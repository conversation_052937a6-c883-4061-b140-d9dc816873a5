/**
 * Fresh Dynamic Agent System Types
 * 
 * Type definitions for the new agent consultation system that integrates with workflows
 */

export type AgentId = 'seo-keyword' | 'market-research' | 'content-strategy';

export type AgentCapability = 
  | 'keyword-research'
  | 'keyword-analysis'
  | 'seo-optimization'
  | 'market-analysis'
  | 'competitor-research'
  | 'audience-analysis'
  | 'content-planning'
  | 'content-strategy'
  | 'content-structure';

export type ConsultationPriority = 'low' | 'medium' | 'high';

export interface AgentConsultationRequest {
  id: string;
  workflowExecutionId: string;
  stepId: string;
  agentId: AgentId;
  question: string;
  context: Record<string, any>;
  priority: ConsultationPriority;
  timeoutMs: number;
  createdAt: string;
}

export interface AgentConsultationResponse {
  consultationId: string;
  agentId: AgentId;
  response: any;
  confidence: number;
  reasoning?: string;
  suggestions: AgentSuggestion[];
  processingTime: number;
  metadata?: Record<string, any>;
}

export interface AgentSuggestion {
  area: string;
  suggestion: string;
  priority: ConsultationPriority;
  confidence?: number;
  actionable?: boolean;
}

export interface AgentCapabilityDefinition {
  id: AgentCapability;
  name: string;
  description: string;
  inputRequirements: string[];
  outputFormat: string;
}

export interface AgentMetrics {
  totalConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageResponseTime: number;
  averageConfidence: number;
  successRate: number;
  agentUtilization: Record<AgentId, number>;
  consultationsByTrigger?: Record<string, number>;
  lastUpdated: string;
}

export interface ConsultationContext {
  topic?: string;
  contentType?: string;
  targetAudience?: string;
  primaryKeyword?: string;
  industry?: string;
  goals?: string[];
  content?: string;
  feedback?: string;
  qualityScore?: number;
  complexity?: number;
  userRequestedConsultation?: boolean;
  [key: string]: any;
}

export interface AgentConfiguration {
  agentId: AgentId;
  capabilities: AgentCapability[];
  maxConcurrentConsultations: number;
  defaultTimeoutMs: number;
  retryAttempts: number;
  enabled: boolean;
}

/**
 * Base interface for all agents in the fresh system
 */
export interface IAgent {
  getAgentId(): AgentId;
  getCapabilities(): AgentCapability[];
  isAvailable(): Promise<boolean>;
  processConsultation(request: AgentConsultationRequest): Promise<AgentConsultationResponse>;
  getConfiguration(): AgentConfiguration;
  updateConfiguration(config: Partial<AgentConfiguration>): void;
}

/**
 * Agent bridge interface for workflow integration
 */
export interface IAgentBridge {
  registerAgent(agent: IAgent): void;
  unregisterAgent(agentId: AgentId): void;
  getAgent(agentId: AgentId): IAgent | null;
  getRegisteredAgents(): AgentId[];
  isAgentAvailable(agentId: AgentId): Promise<boolean>;
  consultAgent(
    agentId: AgentId,
    request: AgentConsultationRequest
  ): Promise<AgentConsultationResponse>;
}

/**
 * Consultation service interface
 */
export interface IConsultationService {
  consultAgent(
    agentId: AgentId,
    workflowExecutionId: string,
    stepId: string,
    context: ConsultationContext
  ): Promise<AgentConsultationResponse>;
  
  consultMultipleAgents(
    workflowExecutionId: string,
    stepId: string,
    context: ConsultationContext,
    config: any
  ): Promise<AgentConsultationResponse[]>;
  
  selectAgentsForContext(context: ConsultationContext): Promise<AgentId[]>;
  
  shouldTriggerConsultation(config: any, context: ConsultationContext): Promise<boolean>;
  
  getMetrics(): AgentMetrics;
  clearMetrics(): void;
}

/**
 * SEO Keyword Agent specific types
 */
export interface SeoKeywordRequest {
  topic: string;
  targetAudience?: string;
  primaryKeyword?: string;
  competitorKeywords?: string[];
  contentType?: string;
  searchVolume?: 'low' | 'medium' | 'high';
}

export interface SeoKeywordResponse {
  primaryKeywords: string[];
  secondaryKeywords: string[];
  longTailKeywords: string[];
  competitorKeywords: string[];
  keywordDifficulty: Record<string, number>;
  searchVolume: Record<string, number>;
  recommendations: string[];
}

/**
 * Market Research Agent specific types
 */
export interface MarketResearchRequest {
  topic: string;
  industry?: string;
  targetAudience?: string;
  geographicScope?: string;
  timeframe?: string;
}

export interface MarketResearchResponse {
  marketSize: string;
  targetDemographics: Record<string, any>;
  competitorAnalysis: Array<{
    name: string;
    strengths: string[];
    weaknesses: string[];
    marketShare?: string;
  }>;
  marketTrends: string[];
  opportunities: string[];
  threats: string[];
  recommendations: string[];
}

/**
 * Content Strategy Agent specific types
 */
export interface ContentStrategyRequest {
  topic: string;
  contentType: string;
  targetAudience: string;
  goals: string[];
  existingContent?: string;
  brandVoice?: string;
  contentPillars?: string[];
}

export interface ContentStrategyResponse {
  contentOutline: Array<{
    section: string;
    purpose: string;
    keyPoints: string[];
  }>;
  contentPillars: string[];
  toneAndVoice: string;
  callToAction: string[];
  distributionStrategy: string[];
  performanceMetrics: string[];
  recommendations: string[];
}

/**
 * Error types for agent system
 */
export class AgentError extends Error {
  constructor(
    message: string,
    public agentId: AgentId,
    public errorCode: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'AgentError';
  }
}

export class ConsultationTimeoutError extends AgentError {
  constructor(agentId: AgentId, timeoutMs: number) {
    super(
      `Consultation with agent ${agentId} timed out after ${timeoutMs}ms`,
      agentId,
      'CONSULTATION_TIMEOUT',
      { timeoutMs }
    );
    this.name = 'ConsultationTimeoutError';
  }
}

export class AgentUnavailableError extends AgentError {
  constructor(agentId: AgentId) {
    super(
      `Agent ${agentId} is currently unavailable`,
      agentId,
      'AGENT_UNAVAILABLE'
    );
    this.name = 'AgentUnavailableError';
  }
}

/**
 * Collaboration types for multi-agent workflows
 */
export interface CollaborationContext {
  artifact: any;
  previousRounds: CollaborationRound[];
  roundNumber: number;
}

export interface CollaborationRound {
  number: number;
  agentInputs: Map<string, AgentInput>;
  peerReviews: Map<string, PeerReview[]>;
  synthesizedResult: any;
}

export interface AgentInput {
  agentId: string;
  roundNumber: number;
  analysis: any;
  suggestions: string[];
  confidence: number;
  reasoning: string;
  collaborationNotes?: string;
}

export interface PeerReview {
  reviewerId: string;
  reviewedAgentId: string;
  agreement: number; // 0-1 scale
  concerns: string[];
  suggestions: string[];
  synergies: string[];
}

export interface ConsensusContribution {
  agentId: string;
  consensusPoints: string[];
  disagreements: string[];
  finalRecommendation: string;
  confidence: number;
}
