/**
 * Simplified Review System Implementation
 * Basic approve/reject functionality with email notifications
 */

import { v4 as uuidv4 } from 'uuid';
import {
  ISimplifiedReviewSystem,
  ReviewOptions,
  ReviewLink,
  ReviewData,
  ReviewSubmission,
  ReviewDecision,
  ReviewStatus,
  ReviewContent,
  ReviewNotification,
  NotificationType,
  ReviewError,
  ReviewNotFoundError,
  ReviewExpiredError,
  ReviewAlreadyCompletedError
} from './types';
import { ISimplifiedStateStore, Review, ReviewType as StateReviewType, ReviewStatus as StateReviewStatus } from '../state/types';

export class SimplifiedReviewSystem implements ISimplifiedReviewSystem {
  constructor(
    private stateStore: ISimplifiedStateStore,
    private baseUrl: string = 'https://cms.authencio.com'
  ) {}

  async createReview(content: string, options: ReviewOptions): Promise<ReviewLink> {
    const reviewId = uuidv4();
    const now = new Date().toISOString();

    // Calculate deadline
    const deadline = options.deadline || this.calculateDefaultDeadline();

    // Store the content first
    const contentRecord = {
      id: options.contentId,
      type: this.inferContentType(options.type),
      title: `Review Content - ${options.stepId}`,
      content: content,
      status: 'pending_review',
      createdAt: now,
      updatedAt: now,
      metadata: {
        executionId: options.executionId,
        stepId: options.stepId,
        reviewType: options.type
      }
    };

    console.log(`📝 Storing content for review: ${options.contentId} (length: ${content.length})`);
    await this.stateStore.setContent(contentRecord);

    // Create review in state
    const review: Review = {
      id: reviewId,
      contentId: options.contentId,
      executionId: options.executionId,
      stepId: options.stepId,
      type: this.mapReviewType(options.type),
      status: StateReviewStatus.PENDING,
      instructions: options.instructions || this.getDefaultInstructions(options.type),
      deadline,
      createdAt: now
    };

    await this.stateStore.setReview(review);

    // Create review link
    const reviewLink: ReviewLink = {
      reviewId,
      url: `${this.baseUrl}/workflow/unified?step=review&reviewId=${reviewId}`,
      expiresAt: deadline,
      accessToken: this.generateAccessToken(reviewId)
    };

    // Send notifications if reviewers specified
    if (options.reviewers && options.reviewers.length > 0) {
      await this.sendReviewNotifications(reviewId, options.reviewers);
    }

    console.log(`✅ Review created successfully: ${reviewId} with content: ${options.contentId}`);
    return reviewLink;
  }

  async getReview(reviewId: string): Promise<ReviewData> {
    const review = await this.stateStore.getReview(reviewId);
    if (!review) {
      throw new ReviewNotFoundError(reviewId);
    }

    // Check if expired
    if (review.deadline && new Date() > new Date(review.deadline)) {
      if (review.status === StateReviewStatus.PENDING) {
        // Mark as expired
        await this.stateStore.setReview({
          ...review,
          status: StateReviewStatus.EXPIRED
        });
        throw new ReviewExpiredError(reviewId);
      }
    }

    // Get content - with fallback handling
    let content = await this.stateStore.getContent(review.contentId);
    console.log(`🔍 Content lookup for ${review.contentId}:`, content ? 'Found' : 'Not found');
    if (content) {
      console.log(`📋 Content details:`, {
        id: content.id,
        type: content.type,
        title: content.title,
        contentLength: typeof content.content === 'string' ? content.content.length : 'Not a string',
        contentPreview: typeof content.content === 'string' ? content.content.substring(0, 100) + '...' : content.content
      });

      // Check if the content field contains a UUID (indicating corrupted data from old system)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (typeof content.content === 'string' && uuidRegex.test(content.content)) {
        console.warn(`⚠️ Content field contains UUID ${content.content}, attempting to find actual content`);

        // Try to find the actual content using the UUID as content ID
        const actualContent = await this.stateStore.getContent(content.content);
        if (actualContent && actualContent.content && !uuidRegex.test(actualContent.content)) {
          console.log(`✅ Found actual content for UUID ${content.content}`);
          content = actualContent;
        } else {
          console.warn(`❌ Could not find actual content for UUID ${content.content}, using fallback mechanism`);
          // Force fallback mechanism by setting content to null
          content = null;
        }
      }
    }

    if (!content) {
      console.warn(`⚠️ Content ${review.contentId} not found for review ${reviewId}, attempting fallback`);

      // Try to get content from execution step results as fallback
      const execution = await this.stateStore.getExecution(review.executionId);
      if (execution && execution.stepResults[review.stepId]) {
        const stepResult = execution.stepResults[review.stepId];

        // Try to extract content from step outputs
        let fallbackContent = '';
        if (stepResult.outputs) {
          // Look for content in various possible keys
          for (const [key, value] of Object.entries(stepResult.outputs)) {
            if (key.includes('content') && typeof value === 'string') {
              fallbackContent = value;
              break;
            }
          }

          // If no content found, use all outputs as JSON
          if (!fallbackContent) {
            fallbackContent = JSON.stringify(stepResult.outputs, null, 2);
          }
        }

        // Create a temporary content object
        content = {
          id: review.contentId,
          type: 'text',
          title: `Review Content - ${review.stepId}`,
          content: fallbackContent || 'No content available for review',
          status: 'pending_review',
          createdAt: review.createdAt,
          updatedAt: review.createdAt,
          metadata: {
            fallback: true,
            executionId: review.executionId,
            stepId: review.stepId
          }
        };

        console.log(`📝 Using fallback content for review ${reviewId} (length: ${fallbackContent.length})`);
      } else {
        throw new ReviewError(`Content ${review.contentId} not found and no fallback available`, 'CONTENT_NOT_FOUND', reviewId);
      }
    }

    // Get execution for context
    const execution = await this.stateStore.getExecution(review.executionId);
    const workflow = execution ? await this.stateStore.getWorkflow(execution.workflowId) : null;

    const reviewContent: ReviewContent = {
      id: content.id,
      type: content.type,
      title: content.title,
      data: content.content,
      context: {
        workflowName: workflow?.name,
        stepName: execution?.stepResults[review.stepId]?.stepId
      }
    };

    return {
      id: review.id,
      content: reviewContent,
      instructions: review.instructions,
      type: this.mapStateReviewType(review.type),
      status: this.mapStateReviewStatus(review.status),
      deadline: review.deadline,
      createdAt: review.createdAt,
      executionId: review.executionId,
      stepId: review.stepId
    };
  }

  async submitReview(reviewId: string, decision: 'approve' | 'reject', edits?: string): Promise<void> {
    const review = await this.stateStore.getReview(reviewId);
    if (!review) {
      throw new ReviewNotFoundError(reviewId);
    }

    if (review.status === StateReviewStatus.COMPLETED) {
      throw new ReviewAlreadyCompletedError(reviewId);
    }

    if (review.deadline && new Date() > new Date(review.deadline)) {
      throw new ReviewExpiredError(reviewId);
    }

    const now = new Date().toISOString();
    
    // Update review
    const updatedReview: Review = {
      ...review,
      status: StateReviewStatus.COMPLETED,
      decision: decision === 'approve' ? 'approved' : 'rejected',
      feedback: edits,
      completedAt: now
    };

    await this.stateStore.setReview(updatedReview);

    // Update content status based on decision
    const content = await this.stateStore.getContent(review.contentId);
    if (content) {
      let updatedContent = { ...content };

      if (decision === 'approve') {
        updatedContent.status = 'approved';
      } else {
        updatedContent.status = 'rejected';
        // Store feedback in metadata, but don't replace content
        if (edits) {
          updatedContent.metadata = {
            ...updatedContent.metadata,
            rejectionFeedback: edits,
            rejectedAt: now
          };
        }
      }

      updatedContent.updatedAt = now;
      await this.stateStore.setContent(updatedContent);
    }

    // Continue workflow execution if approved
    if (decision === 'approve') {
      await this.continueWorkflowExecution(review.executionId, review.stepId);
    }

    // Send completion notification
    await this.sendCompletionNotification(reviewId, decision);
  }

  // Helper methods
  private mapReviewType(type: ReviewOptions['type']): StateReviewType {
    switch (type) {
      case 'approval':
        return StateReviewType.APPROVAL;
      case 'editing':
        return StateReviewType.EDITING;
      case 'feedback':
        return StateReviewType.FEEDBACK;
      default:
        return StateReviewType.APPROVAL;
    }
  }

  private mapStateReviewType(type: StateReviewType): ReviewOptions['type'] {
    switch (type) {
      case StateReviewType.APPROVAL:
        return 'approval';
      case StateReviewType.EDITING:
        return 'editing';
      case StateReviewType.FEEDBACK:
        return 'feedback';
      default:
        return 'approval';
    }
  }

  private mapStateReviewStatus(status: StateReviewStatus): ReviewStatus {
    switch (status) {
      case StateReviewStatus.PENDING:
        return ReviewStatus.PENDING;
      case StateReviewStatus.IN_PROGRESS:
        return ReviewStatus.IN_PROGRESS;
      case StateReviewStatus.COMPLETED:
        return ReviewStatus.COMPLETED;
      case StateReviewStatus.EXPIRED:
        return ReviewStatus.EXPIRED;
      default:
        return ReviewStatus.PENDING;
    }
  }

  private calculateDefaultDeadline(): string {
    const deadline = new Date();
    deadline.setHours(deadline.getHours() + 24); // 24 hours default
    return deadline.toISOString();
  }

  private getDefaultInstructions(type: ReviewOptions['type']): string {
    switch (type) {
      case 'approval':
        return 'Please review the content and approve or reject it. Provide feedback if rejecting.';
      case 'editing':
        return 'Please review and edit the content as needed. Make any necessary improvements.';
      case 'feedback':
        return 'Please provide feedback on the content. Suggest improvements or changes.';
      default:
        return 'Please review the content.';
    }
  }

  private inferContentType(reviewType: ReviewOptions['type']): string {
    switch (reviewType) {
      case 'approval':
        return 'content';
      case 'editing':
        return 'draft';
      case 'feedback':
        return 'text';
      default:
        return 'text';
    }
  }

  private generateAccessToken(reviewId: string): string {
    // Simple token generation - in production, use proper JWT or similar
    return Buffer.from(`${reviewId}:${Date.now()}`).toString('base64');
  }

  private async sendReviewNotifications(reviewId: string, reviewers: string[]): Promise<void> {
    // Simple notification - in production, integrate with email service
    console.log(`Sending review notifications for ${reviewId} to:`, reviewers);

    // Here you would integrate with email service like SendGrid, AWS SES, etc.
    // For now, just log the notification
    const reviewLink = `${this.baseUrl}/workflow/unified?step=review&reviewId=${reviewId}`;
    
    for (const reviewer of reviewers) {
      const notification: ReviewNotification = {
        type: NotificationType.REVIEW_REQUESTED,
        reviewId,
        recipient: reviewer,
        subject: 'Content Review Required',
        message: `You have been requested to review content. Please visit: ${reviewLink}`,
        link: reviewLink
      };
      
      // In production, send actual email
      console.log('Email notification:', notification);
    }
  }

  private async sendCompletionNotification(reviewId: string, decision: string): Promise<void> {
    console.log(`Review ${reviewId} completed with decision: ${decision}`);
    
    // In production, notify workflow system or other stakeholders
  }

  private async continueWorkflowExecution(executionId: string, stepId: string): Promise<void> {
    // Get execution
    const execution = await this.stateStore.getExecution(executionId);
    if (!execution) {
      return;
    }

    // Mark step as completed
    const updatedExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: {
          ...execution.stepResults[stepId],
          status: 'completed' as any,
          completedAt: new Date().toISOString()
        }
      }
    };

    await this.stateStore.setExecution(updatedExecution);

    // In a full implementation, this would trigger the workflow engine
    // to continue with the next step
    console.log(`Continuing workflow execution ${executionId} after step ${stepId}`);
  }

  // Public utility methods
  async getPendingReviews(): Promise<Review[]> {
    return await this.stateStore.getPendingReviews();
  }

  async getReviewsByStatus(status: StateReviewStatus): Promise<Review[]> {
    const state = await this.stateStore.get();
    if (!state) return [];
    
    return Object.values(state.reviews).filter(review => review.status === status);
  }

  async expireOverdueReviews(): Promise<void> {
    const pendingReviews = await this.getPendingReviews();
    const now = new Date();

    for (const review of pendingReviews) {
      if (review.deadline && now > new Date(review.deadline)) {
        await this.stateStore.setReview({
          ...review,
          status: StateReviewStatus.EXPIRED
        });
      }
    }
  }

  // Additional methods for enhanced API support
  async getAllReviews(): Promise<any[]> {
    const state = await this.stateStore.get();
    if (!state) return [];

    return Object.values(state.reviews).map(review => ({
      ...review,
      reviewerAssignments: review.reviewerAssignments || [],
      reviewHistory: review.reviewHistory || [],
      priority: review.priority || 'medium',
      estimatedTime: review.estimatedTime || 30,
      artifactVersion: review.artifactVersion || 1
    }));
  }

  async updateReviewPriority(reviewId: string, priority: string): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;
      state.reviews[reviewId].priority = priority as any;
      return state;
    });
  }

  async assignReviewer(reviewId: string, reviewerId: string, assignedBy: string = 'system', options: any = {}): Promise<any> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;

      const review = state.reviews[reviewId];
      if (!review.reviewerAssignments) {
        review.reviewerAssignments = [];
      }

      if (!review.reviewerAssignments.includes(reviewerId)) {
        review.reviewerAssignments.push(reviewerId);
      }

      return state;
    });

    return {
      reviewId,
      reviewerId,
      assignedBy,
      assignedAt: new Date().toISOString(),
      ...options
    };
  }

  async updateReview(reviewId: string, updates: any): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;
      Object.assign(state.reviews[reviewId], updates);
      return state;
    });
  }

  async deleteReview(reviewId: string): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;
      delete state.reviews[reviewId];
      return state;
    });
  }

  async updateDeadline(reviewId: string, deadline: string): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;
      state.reviews[reviewId].deadline = deadline;
      return state;
    });
  }

  async removeReviewerAssignment(reviewId: string, reviewerId: string): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;

      const review = state.reviews[reviewId];
      if (review.reviewerAssignments) {
        review.reviewerAssignments = review.reviewerAssignments.filter(id => id !== reviewerId);
      }

      return state;
    });
  }

  async getReviewAssignments(reviewId: string): Promise<any[]> {
    const state = await this.stateStore.get();
    if (!state || !state.reviews[reviewId]) return [];

    const review = state.reviews[reviewId];
    return (review.reviewerAssignments || []).map(reviewerId => ({
      id: `assignment_${reviewId}_${reviewerId}`,
      reviewId,
      reviewerId,
      status: 'pending',
      assignedAt: new Date().toISOString()
    }));
  }

  async updateReviewerAssignment(reviewId: string, reviewerId: string, updates: any): Promise<void> {
    // For now, just update the review itself
    await this.updateReview(reviewId, updates);
  }

  async addComment(reviewId: string, comment: any): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;

      const review = state.reviews[reviewId];
      if (!review.comments) {
        review.comments = [];
      }

      review.comments.push(comment);
      return state;
    });
  }

  async getComments(reviewId: string): Promise<any[]> {
    const state = await this.stateStore.get();
    if (!state || !state.reviews[reviewId]) return [];

    return state.reviews[reviewId].comments || [];
  }

  async getComment(reviewId: string, commentId: string): Promise<any> {
    const comments = await this.getComments(reviewId);
    return comments.find(comment => comment.id === commentId);
  }

  async updateComment(reviewId: string, commentId: string, updates: any): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;

      const review = state.reviews[reviewId];
      if (review.comments) {
        const commentIndex = review.comments.findIndex(comment => comment.id === commentId);
        if (commentIndex >= 0) {
          Object.assign(review.comments[commentIndex], updates);
        }
      }

      return state;
    });
  }

  async deleteComment(reviewId: string, commentId: string): Promise<void> {
    await this.stateStore.update(state => {
      if (!state || !state.reviews[reviewId]) return state;

      const review = state.reviews[reviewId];
      if (review.comments) {
        review.comments = review.comments.filter(comment => comment.id !== commentId);
      }

      return state;
    });
  }
}
