import { CollectionConfig } from 'payload'

export const FeatureComparisons: CollectionConfig = {
  slug: 'feature-comparisons',
  admin: {
    useAsTitle: 'name',
    group: 'Comparison Content',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'Core Features', value: 'core' },
        { label: 'Integration', value: 'integration' },
        { label: 'Security', value: 'security' },
        { label: 'Support', value: 'support' },
        { label: 'Pricing', value: 'pricing' },
      ],
    },
    {
      name: 'order',
      type: 'number',
      admin: {
        description: 'Display order within category',
      }
    },
    {
      name: 'comparisons',
      type: 'array',
      fields: [
        {
          name: 'product',
          type: 'relationship',
          relationTo: 'products',
        },
        {
          name: 'value',
          type: 'group',
          fields: [
            {
              name: 'type',
              type: 'select',
              options: [
                { label: 'Boolean', value: 'boolean' },
                { label: 'Text', value: 'text' },
              ],
            },
            {
              name: 'booleanValue',
              type: 'checkbox',
              admin: {
                condition: (data, siblingData) => siblingData?.type === 'boolean',
              },
            },
            {
              name: 'textValue',
              type: 'text',
              admin: {
                condition: (data, siblingData) => siblingData?.type === 'text',
              },
            }
          ]
        }
      ]
    },
    {
      name: 'notes',
      type: 'textarea', // Changed from richText to textarea temporarily
    },
    {
      name: 'isPlanComparison',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Enable if this is for comparing different plans of the same product',
      }
    },
    {
      name: 'planComparisonProduct',
      type: 'relationship',
      relationTo: 'products',
      admin: {
        condition: (data, siblingData) => siblingData?.isPlanComparison === true,
        description: 'The product whose plans are being compared',
      }
    }
  ]
}