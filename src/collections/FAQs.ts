import { CollectionConfig } from 'payload'

export const FAQs: CollectionConfig = {
  slug: 'faqs',
  admin: {
    useAsTitle: 'question',
    group: 'Product Content',
  },
  fields: [
    {
      name: 'question',
      type: 'text',
      required: true,
    },
    {
      name: 'answer',
      type: 'textarea', // Changed from richText to textarea temporarily
      required: true,
      admin: {
        components: {
          Field: {
            path: '@/components/AI/AIHelperField',
            exportName: 'AIHelperField'
          }
        }
      }
    },
    {
      name: 'product',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'General', value: 'general' },
        { label: 'Features', value: 'features' },
        { label: 'Pricing', value: 'pricing' },
        { label: 'Support', value: 'support' },
        { label: 'Implementation', value: 'implementation' },
        { label: 'Alternatives', value: 'alternatives' },
      ],
      required: true,
    },
    {
      name: 'order',
      type: 'number',
      admin: {
        description: 'Display order within category',
      }
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'pageType',
      type: 'select',
      options: [
        { label: 'All', value: 'all' },
        { label: 'Overview', value: 'overview' },
        { label: 'Features', value: 'features' },
        { label: 'Pricing', value: 'pricing' },
        { label: 'Reviews', value: 'reviews' },
        { label: 'Alternatives', value: 'alternatives' },
      ],
      admin: {
        description: 'Which page type should this FAQ appear on?',
      },
      defaultValue: 'all',
    }
  ]
}