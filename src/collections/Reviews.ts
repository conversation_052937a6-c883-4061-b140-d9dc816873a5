import { CollectionConfig } from 'payload'

export const Reviews: CollectionConfig = {
  slug: 'reviews',
  admin: {
    useAsTitle: 'title',
    group: 'Product Content',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'product',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'authors',
      required: true,
    },
    {
      name: 'publishedDate',
      type: 'date',
      required: true,
    },
    {
      name: 'content',
      type: 'textarea', // Changed from richText to textarea temporarily
      required: true,
      admin: {
        components: {
          Field: {
            path: '@/components/AI/AIHelperField',
            exportName: 'AIHelperField'
          }
        }
      }
    },
    {
      name: 'rating',
      type: 'group',
      fields: [
        {
          name: 'overall',
          type: 'number',
          min: 0,
          max: 5,
          required: true,
        },
        {
          name: 'usability',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'features',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'support',
          type: 'number',
          min: 0,
          max: 5,
        },
        {
          name: 'value',
          type: 'number',
          min: 0,
          max: 5,
        }
      ]
    },
    {
      name: 'pros',
      type: 'array',
      fields: [
        {
          name: 'pro',
          type: 'text',
        }
      ]
    },
    {
      name: 'cons',
      type: 'array',
      fields: [
        {
          name: 'con',
          type: 'text',
        }
      ]
    },
    {
      name: 'verdict',
      type: 'textarea',
      required: true,
    },
    {
      name: 'highlights',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
        },
        {
          name: 'icon',
          type: 'select',
          options: [
            { label: 'Clock', value: 'Clock' },
            { label: 'Gift', value: 'Gift' },
            { label: 'Users', value: 'Users' },
            { label: 'Shield', value: 'Shield' },
            { label: 'Zap', value: 'Zap' },
            { label: 'Star', value: 'Star' },
          ],
        },
        {
          name: 'themeColor',
          type: 'select',
          options: [
            { label: 'Blue', value: 'blue' },
            { label: 'Purple', value: 'purple' },
            { label: 'Indigo', value: 'indigo' },
          ],
          defaultValue: 'blue',
        }
      ]
    },
    {
      name: 'usabilityScores',
      type: 'array',
      fields: [
        {
          name: 'label',
          type: 'text',
          required: true,
        },
        {
          name: 'rating',
          type: 'number',
          min: 0,
          max: 5,
          required: true,
        },
        {
          name: 'comment',
          type: 'text',
        }
      ]
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Under Review', value: 'review' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
      required: true,
      admin: {
        position: 'sidebar',
      }
    }
  ]
}