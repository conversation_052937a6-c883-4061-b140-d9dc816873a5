import { CollectionConfig } from 'payload'

export const MigrationConsiderations: CollectionConfig = {
  slug: 'migration-considerations',
  admin: {
    useAsTitle: 'title',
    group: 'Comparison Content',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'fromProduct',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
    {
      name: 'toProduct',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
    {
      name: 'complexity',
      type: 'select',
      options: [
        { label: 'Simple', value: 'simple' },
        { label: 'Moderate', value: 'moderate' },
        { label: 'Complex', value: 'complex' },
      ],
      required: true,
    },
    {
      name: 'estimatedTime',
      type: 'text',
      admin: {
        description: 'E.g., "2-4 weeks"',
      }
    },
    {
      name: 'tools',
      type: 'array',
      fields: [
        {
          name: 'name',
          type: 'text',
        },
        {
          name: 'description',
          type: 'text',
        }
      ]
    },
    {
      name: 'steps',
      type: 'array',
      fields: [
        {
          name: 'step',
          type: 'textarea', // Changed from richText to textarea temporarily
        }
      ]
    },
    {
      name: 'migrationResources',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
        },
        {
          name: 'url',
          type: 'text',
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'resourceType',
          type: 'select',
          options: [
            { label: 'Guide', value: 'guide' },
            { label: 'Video', value: 'video' },
            { label: 'Tool', value: 'tool' },
            { label: 'Documentation', value: 'documentation' },
          ],
        },
      ]
    },
    {
      name: 'considerations',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
        }
      ]
    }
  ]
}