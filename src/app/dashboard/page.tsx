'use client';

import React, { useState } from 'react';
import {
  CogIcon,
  BeakerIcon,
  SparklesIcon,
  CpuChipIcon,
  ChartBarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import WorkflowTestingInterface from '../../components/Admin/WorkflowTestingInterface';
import ConsultationPatternsShowcase from '../../components/Admin/ConsultationPatternsShowcase';
import ContentComplexityAnalyzer from '../../components/Admin/ContentComplexityAnalyzer';
import EnhancedConsultationMetrics from '../../components/Workflow/EnhancedConsultationMetrics';
import TemplateValidationPanel from '../../components/Workflow/TemplateValidationPanel';
import BulkOperationsInterface from '../../components/Workflow/BulkOperationsInterface';
import WorkflowAnalyticsDashboard from '../../components/Workflow/WorkflowAnalyticsDashboard';
import MultiReviewerApprovalSystem from '../../components/Workflow/MultiReviewerApprovalSystem';
import RealTimeCollaborationVisualization from '../../components/Workflow/RealTimeCollaborationVisualization';

type AdminTab = 'overview' | 'testing' | 'patterns' | 'complexity' | 'metrics' | 'templates' | 'bulk' | 'analytics' | 'approval' | 'collaboration';

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<AdminTab>('overview');

  const tabs = [
    {
      id: 'overview' as AdminTab,
      name: 'Overview',
      icon: CogIcon,
      description: 'System overview and quick stats'
    },
    {
      id: 'testing' as AdminTab,
      name: 'Testing',
      icon: BeakerIcon,
      description: 'Workflow testing and validation'
    },
    {
      id: 'patterns' as AdminTab,
      name: 'Consultation Patterns',
      icon: SparklesIcon,
      description: 'Agent consultation patterns and configurations'
    },
    {
      id: 'complexity' as AdminTab,
      name: 'Complexity Analysis',
      icon: CpuChipIcon,
      description: 'Content complexity scoring and analysis'
    },
    {
      id: 'metrics' as AdminTab,
      name: 'Metrics',
      icon: ChartBarIcon,
      description: 'Agent consultation metrics and analytics'
    },
    {
      id: 'templates' as AdminTab,
      name: 'Template Validation',
      icon: DocumentTextIcon,
      description: 'Template validation and execution order'
    },
    {
      id: 'bulk' as AdminTab,
      name: 'Bulk Operations',
      icon: DocumentTextIcon,
      description: 'CSV import/export and batch processing'
    },
    {
      id: 'analytics' as AdminTab,
      name: 'Analytics',
      icon: ChartBarIcon,
      description: 'Workflow analytics and performance metrics'
    },
    {
      id: 'approval' as AdminTab,
      name: 'Multi-Reviewer',
      icon: CogIcon,
      description: 'Multi-reviewer approval system'
    },
    {
      id: 'collaboration' as AdminTab,
      name: 'Real-Time Collaboration',
      icon: CogIcon,
      description: 'Live agent collaboration visualization'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">System Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <BeakerIcon className="h-8 w-8 text-blue-600" />
                    <div>
                      <div className="text-lg font-semibold text-blue-900">Workflow Testing</div>
                      <div className="text-sm text-blue-700">Test system functionality</div>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <SparklesIcon className="h-8 w-8 text-green-600" />
                    <div>
                      <div className="text-lg font-semibold text-green-900">Agent Consultation</div>
                      <div className="text-sm text-green-700">Dynamic agent patterns</div>
                    </div>
                  </div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CpuChipIcon className="h-8 w-8 text-purple-600" />
                    <div>
                      <div className="text-lg font-semibold text-purple-900">Content Analysis</div>
                      <div className="text-sm text-purple-700">Complexity scoring</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Backend Capabilities Showcase</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Enhanced Features</h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>Dynamic Agent Consultation Service</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>Enhanced Template Validation</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>Content Complexity Assessment</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>Consultation Metrics by Trigger</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>User-Requested Consultations</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Testing & Utilities</h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Workflow Integration Testing</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Agent Consultation Testing</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>System Health Monitoring</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Template Execution Order Analysis</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>Consultation Pattern Examples</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {tabs.slice(1).map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
                  >
                    <tab.icon className="h-6 w-6 text-gray-600 mb-2" />
                    <div className="font-medium text-gray-900">{tab.name}</div>
                    <div className="text-xs text-gray-600 mt-1">{tab.description}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 'testing':
        return <WorkflowTestingInterface />;

      case 'patterns':
        return <ConsultationPatternsShowcase />;

      case 'complexity':
        return <ContentComplexityAnalyzer />;

      case 'metrics':
        return <EnhancedConsultationMetrics showTriggerAnalysis={true} showAgentUtilization={true} />;

      case 'templates':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Template Validation Testing</h2>
              <p className="text-gray-600 mb-6">
                Test template validation with different template configurations to see validation results and execution order analysis.
              </p>

              <TemplateValidationPanel
                template={{
                  id: 'blog-post-seo',
                  name: 'SEO Blog Post Template',
                  description: 'Complete SEO-optimized blog post generation with keyword research and human review',
                  instructions: 'This template creates a comprehensive SEO-optimized blog post with agent consultation and human review.',
                  featured: true,
                  sampleInputs: {
                    topic: 'AI in real estate',
                    target_audience: 'real estate firms',
                    primary_keyword: 'AI real estate technology'
                  },
                  workflow: {
                    name: 'SEO Blog Post',
                    description: 'Complete SEO-optimized blog post generation with keyword research and human review',
                    version: '1.0.0',
                    metadata: {
                      category: 'blog',
                      difficulty: 'easy',
                      estimatedTime: 45,
                      tags: ['seo', 'blog', 'content-marketing']
                    },
                    steps: [
                      {
                        id: 'topic-input',
                        name: 'Topic Input',
                        type: 'TEXT_INPUT',
                        config: {},
                        inputs: [],
                        outputs: ['topic', 'target_audience', 'primary_keyword'],
                        dependencies: []
                      },
                      {
                        id: 'keyword-research',
                        name: 'Keyword Research',
                        type: 'AI_GENERATION',
                        config: {},
                        inputs: ['topic', 'target_audience', 'primary_keyword'],
                        outputs: ['keyword_research'],
                        dependencies: ['topic-input'],
                        consultationConfig: {
                          enabled: true,
                          triggers: [{ type: 'always', agents: ['seo-keyword'], priority: 'high' }],
                          maxConsultations: 2,
                          timeoutMs: 30000,
                          fallbackBehavior: 'continue'
                        }
                      },
                      {
                        id: 'content-creation',
                        name: 'Content Creation',
                        type: 'AI_GENERATION',
                        config: {},
                        inputs: ['topic', 'target_audience', 'keyword_research'],
                        outputs: ['blog_content'],
                        dependencies: ['keyword-research'],
                        consultationConfig: {
                          enabled: true,
                          triggers: [
                            { type: 'content_complexity', condition: { threshold: 0.6 }, agents: ['content-strategy'], priority: 'medium' },
                            { type: 'quality_threshold', condition: { threshold: 0.8 }, agents: ['seo-keyword'], priority: 'low' }
                          ],
                          maxConsultations: 3,
                          timeoutMs: 45000,
                          fallbackBehavior: 'continue'
                        }
                      },
                      {
                        id: 'human-review',
                        name: 'Human Review',
                        type: 'HUMAN_REVIEW',
                        config: {},
                        inputs: ['blog_content'],
                        outputs: ['reviewed_content'],
                        dependencies: ['content-creation']
                      },
                      {
                        id: 'seo-optimization',
                        name: 'SEO Final Check',
                        type: 'AI_GENERATION',
                        config: {},
                        inputs: ['reviewed_content'],
                        outputs: ['seo_optimized_content'],
                        dependencies: ['human-review']
                      },
                      {
                        id: 'results-compilation',
                        name: 'Results Compilation',
                        type: 'AI_GENERATION',
                        config: {},
                        inputs: ['seo_optimized_content'],
                        outputs: ['final_content'],
                        dependencies: ['seo-optimization']
                      },
                      {
                        id: 'cms-publishing',
                        name: 'CMS Publishing',
                        type: 'CMS_INTEGRATION',
                        config: {},
                        inputs: ['final_content'],
                        outputs: ['published_url'],
                        dependencies: ['results-compilation']
                      },
                      {
                        id: 'workflow-completion',
                        name: 'Workflow Completion',
                        type: 'COMPLETION',
                        config: {},
                        inputs: ['published_url'],
                        outputs: ['completion_status'],
                        dependencies: ['cms-publishing']
                      }
                    ],
                    metadata: {
                      category: 'blog',
                      difficulty: 'easy',
                      estimatedTime: 45,
                      tags: ['seo', 'blog', 'content-marketing']
                    }
                  }
                }}
                onValidationComplete={(isValid, result) => {
                  console.log('Template validation completed:', { isValid, result });
                }}
              />
            </div>
          </div>
        );

      case 'bulk':
        return <BulkOperationsInterface />;

      case 'analytics':
        return <WorkflowAnalyticsDashboard />;

      case 'approval':
        return <MultiReviewerApprovalSystem />;

      case 'collaboration':
        return <RealTimeCollaborationVisualization />;

      default:
        return <div>Tab content not found</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600 mt-2">
                Comprehensive backend functionality showcase and testing interface
              </p>
            </div>
            <a
              href="/workflow/unified"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              ← Back to Workflows
            </a>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-1 bg-white rounded-lg border border-gray-200 p-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mb-8">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}
