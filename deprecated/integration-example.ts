/**
 * Integration Example: Fresh Dynamic Agent Consultation System
 * 
 * This example demonstrates how to integrate the fresh agent consultation system
 * with the existing workflow engine for enhanced content generation.
 */

import { EnhancedAIGenerationStep } from './enhanced-ai-generation-step';
import { WorkflowStep, StepType, AgentConsultationConfig } from './types';

/**
 * Example: Enhanced Blog Post Creation Workflow
 * 
 * This example shows how to create a workflow step that uses the fresh
 * dynamic agent consultation system to enhance blog post creation.
 */
export async function createEnhancedBlogPostWorkflow(): Promise<{
  step: WorkflowStep;
  enhancedAIStep: EnhancedAIGenerationStep;
}> {
  // Initialize the enhanced AI generation step
  const enhancedAIStep = new EnhancedAIGenerationStep();

  // Configure agent consultation for comprehensive content enhancement
  const consultationConfig: AgentConsultationConfig = {
    enabled: true,
    triggers: [
      // Always consult SEO and market research agents for blog posts
      {
        type: 'always',
        agents: ['seo-keyword', 'market-research'],
        priority: 'high'
      },
      // Consult content strategy agent for complex topics
      {
        type: 'content_complexity',
        condition: { threshold: 0.6 },
        agents: ['content-strategy'],
        priority: 'medium'
      },
      // Consult SEO agent when feedback mentions SEO issues
      {
        type: 'feedback_keywords',
        condition: { keywords: ['seo', 'keywords', 'optimization', 'ranking'] },
        agents: ['seo-keyword'],
        priority: 'high'
      },
      // Consult strategy agent when quality is below threshold
      {
        type: 'quality_threshold',
        condition: { threshold: 0.8 },
        agents: ['content-strategy'],
        priority: 'medium'
      }
    ],
    maxConsultations: 3,
    timeoutMs: 30000,
    fallbackBehavior: 'continue'
  };

  // Define the enhanced workflow step
  const step: WorkflowStep = {
    id: 'enhanced-blog-post-creation',
    name: 'Enhanced Blog Post Creation with Agent Consultation',
    type: StepType.AI_GENERATION,
    config: {
      aiConfig: {
        provider: 'openai',
        model: 'gpt-4',
        prompt: `Write a comprehensive, engaging blog post about "{{topic}}" for {{targetAudience}}.

The blog post should:
- Be informative and well-structured
- Include relevant examples and insights
- Be optimized for search engines
- Match the target audience's interests and knowledge level
- Include a compelling introduction and conclusion
- Use appropriate headings and subheadings

Topic: {{topic}}
Target Audience: {{targetAudience}}
Content Type: {{contentType}}
Primary Keyword: {{primaryKeyword}}
Goals: {{goals}}`,
        temperature: 0.7,
        maxTokens: 2000
      }
    },
    inputs: [
      'topic',
      'targetAudience', 
      'contentType',
      'primaryKeyword',
      'goals',
      'industry',
      'brandVoice'
    ],
    outputs: ['enhancedContent'],
    dependencies: [],
    consultationConfig
  };

  return { step, enhancedAIStep };
}

/**
 * Example: Execute Enhanced Blog Post Creation
 * 
 * This function demonstrates how to execute the enhanced blog post creation
 * with agent consultation in a real workflow context.
 */
export async function executeEnhancedBlogPostCreation(
  topic: string,
  targetAudience: string,
  options: {
    primaryKeyword?: string;
    industry?: string;
    goals?: string[];
    brandVoice?: string;
    feedback?: string;
    qualityScore?: number;
  } = {}
): Promise<{
  content: any;
  consultationResults: any[];
  metrics: any;
  agentInsights: any;
}> {
  // Create the enhanced workflow
  const { step, enhancedAIStep } = await createEnhancedBlogPostWorkflow();

  // Prepare inputs
  const inputs = {
    topic,
    targetAudience,
    contentType: 'blog-post',
    primaryKeyword: options.primaryKeyword || topic,
    goals: options.goals || ['inform', 'engage', 'convert'],
    industry: options.industry,
    brandVoice: options.brandVoice || 'professional and engaging',
    feedback: options.feedback,
    qualityScore: options.qualityScore
  };

  // Execute the enhanced AI generation with agent consultation
  const result = await enhancedAIStep.executeAIGenerationWithConsultation(
    step,
    inputs,
    `execution-${Date.now()}`
  );

  // Get consultation metrics
  const metrics = enhancedAIStep.getConsultationMetrics();

  // Get agent status for monitoring
  const agentStatus = await enhancedAIStep.getAgentStatus();

  return {
    content: result.outputs.enhancedContent,
    consultationResults: result.consultationResults || [],
    metrics,
    agentInsights: result.outputs.agentInsights || {}
  };
}

/**
 * Example: Feedback-Driven Content Improvement
 * 
 * This example shows how the system responds to human feedback by
 * consulting appropriate agents for content improvement.
 */
export async function improveContentWithFeedback(
  originalContent: any,
  feedback: string,
  context: {
    topic: string;
    targetAudience: string;
    contentType: string;
  }
): Promise<{
  improvedContent: any;
  consultationResults: any[];
  improvements: string[];
}> {
  const { step, enhancedAIStep } = await createEnhancedBlogPostWorkflow();

  // Prepare inputs with feedback
  const inputs = {
    ...context,
    content: originalContent,
    feedback,
    qualityScore: 0.6 // Low score to trigger quality threshold consultation
  };

  // Execute with feedback-driven consultation
  const result = await enhancedAIStep.executeAIGenerationWithConsultation(
    step,
    inputs,
    `improvement-${Date.now()}`
  );

  // Extract improvements from consultation results
  const improvements = (result.consultationResults || [])
    .flatMap(consultation => consultation.suggestions)
    .filter(suggestion => suggestion.priority === 'high')
    .map(suggestion => suggestion.suggestion);

  return {
    improvedContent: result.outputs.enhancedContent,
    consultationResults: result.consultationResults || [],
    improvements
  };
}

/**
 * Example: System Health Monitoring
 * 
 * This example shows how to monitor the health and performance
 * of the agent consultation system.
 */
export async function monitorSystemHealth(): Promise<{
  overallHealth: string;
  agentStatus: any[];
  metrics: any;
  recommendations: string[];
}> {
  const enhancedAIStep = new EnhancedAIGenerationStep();

  // Perform health check
  const healthCheck = await enhancedAIStep.performHealthCheck();
  
  // Get agent status
  const agentStatus = await enhancedAIStep.getAgentStatus();
  
  // Get consultation metrics
  const metrics = enhancedAIStep.getConsultationMetrics();

  // Generate recommendations based on health and metrics
  const recommendations: string[] = [];
  
  if (healthCheck.overallHealth === 'degraded') {
    recommendations.push('Some agents are experiencing issues - check agent logs');
  }
  
  if (metrics.successRate < 0.9) {
    recommendations.push('Consultation success rate is below 90% - consider increasing timeouts');
  }
  
  if (metrics.averageResponseTime > 10000) {
    recommendations.push('Average response time is high - consider optimizing agent performance');
  }

  return {
    overallHealth: healthCheck.overallHealth,
    agentStatus,
    metrics,
    recommendations
  };
}

/**
 * Example: Custom Agent Configuration
 * 
 * This example shows how to customize agent behavior for specific use cases.
 */
export function createCustomConsultationConfig(
  useCase: 'technical-content' | 'marketing-content' | 'general-content'
): AgentConsultationConfig {
  const baseConfig: AgentConsultationConfig = {
    enabled: true,
    triggers: [],
    maxConsultations: 3,
    timeoutMs: 30000,
    fallbackBehavior: 'continue'
  };

  switch (useCase) {
    case 'technical-content':
      baseConfig.triggers = [
        {
          type: 'always',
          agents: ['content-strategy'],
          priority: 'high'
        },
        {
          type: 'content_complexity',
          condition: { threshold: 0.5 },
          agents: ['seo-keyword'],
          priority: 'medium'
        }
      ];
      break;

    case 'marketing-content':
      baseConfig.triggers = [
        {
          type: 'always',
          agents: ['seo-keyword', 'market-research'],
          priority: 'high'
        },
        {
          type: 'quality_threshold',
          condition: { threshold: 0.8 },
          agents: ['content-strategy'],
          priority: 'medium'
        }
      ];
      break;

    case 'general-content':
      baseConfig.triggers = [
        {
          type: 'feedback_keywords',
          condition: { keywords: ['improve', 'better', 'enhance'] },
          agents: ['content-strategy'],
          priority: 'medium'
        },
        {
          type: 'quality_threshold',
          condition: { threshold: 0.7 },
          agents: ['seo-keyword', 'market-research'],
          priority: 'low'
        }
      ];
      break;
  }

  return baseConfig;
}

// Export usage examples for documentation
export const USAGE_EXAMPLES = {
  createEnhancedBlogPostWorkflow,
  executeEnhancedBlogPostCreation,
  improveContentWithFeedback,
  monitorSystemHealth,
  createCustomConsultationConfig
};
