/**
 * DEPRECATED: Workflow Agent Consultation Service
 *
 * This file has been superseded by dynamic-agent-consultation-service.ts
 * Contains stub types and deprecated imports - moved to deprecated folder
 */

import { v4 as uuidv4 } from 'uuid';
// Stub types since the original imports are deprecated
type AgentId = string;
interface Consultation {
  id: string;
  agentId: AgentId;
  query: string;
  response?: any;
  status: 'pending' | 'completed' | 'failed';
  confidence?: number;
  reasoning?: string;
  suggestions?: string[];
}

// Stub ConsultationManager for compatibility
class ConsultationManager {
  async requestConsultation(
    sessionId: string,
    requester: string,
    agentId: AgentId,
    question: string,
    context: any
  ): Promise<string> {
    // Return a mock consultation ID
    return `consultation-${Date.now()}`;
  }

  async getConsultation(sessionId: string, consultationId: string): Promise<Consultation | null> {
    // Return a mock consultation response
    return {
      id: consultationId,
      agentId: 'mock-agent',
      query: 'mock query',
      response: 'This is a mock response for compatibility.',
      status: 'completed',
      confidence: 0.8,
      reasoning: 'Mock reasoning for compatibility',
      suggestions: ['Mock suggestion 1', 'Mock suggestion 2']
    };
  }
}

export interface AgentConsultationConfig {
  enabled: boolean;
  triggers: ConsultationTrigger[];
  maxConsultations: number;
  timeoutMs: number;
  fallbackBehavior: 'continue' | 'fail' | 'retry';
}

export interface ConsultationTrigger {
  type: 'always' | 'quality_threshold' | 'feedback_keywords' | 'content_complexity' | 'user_request';
  condition?: any;
  agents: AgentId[];
  priority: 'low' | 'medium' | 'high';
}

export interface AgentRequirement {
  agentId: AgentId;
  expertise: string[];
  required: boolean;
  weight: number;
}

export interface ConsultationRequest {
  id: string;
  workflowExecutionId: string;
  stepId: string;
  stepType: string;
  question: string;
  context: Record<string, any>;
  requiredAgents: AgentId[];
  priority: 'low' | 'medium' | 'high';
  timeoutMs: number;
  createdAt: string;
}

export interface ConsultationResult {
  consultationId: string;
  agentId: AgentId;
  response: any;
  confidence: number;
  reasoning?: any;
  suggestions?: Array<{
    area: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  processingTime: number;
}

export interface ConsultationMetrics {
  totalConsultations: number;
  successRate: number;
  averageResponseTime: number;
  qualityImprovementScore: number;
  agentUtilization: Record<AgentId, number>;
  consultationsByTrigger: Record<string, number>;
}

export class WorkflowAgentConsultationService {
  private consultationManager: ConsultationManager;
  private activeConsultations = new Map<string, ConsultationRequest>();
  private consultationResults = new Map<string, ConsultationResult[]>();
  private metrics: ConsultationMetrics;

  constructor() {
    this.consultationManager = new ConsultationManager();
    this.metrics = {
      totalConsultations: 0,
      successRate: 0,
      averageResponseTime: 0,
      qualityImprovementScore: 0,
      agentUtilization: {},
      consultationsByTrigger: {}
    };
  }

  /**
   * Request agent consultation for a workflow step
   */
  async consultAgentForStep(
    workflowExecutionId: string,
    stepId: string,
    stepType: string,
    context: Record<string, any>,
    config: AgentConsultationConfig
  ): Promise<ConsultationResult[]> {
    try {
      console.log(`🤝 Requesting agent consultation for step ${stepId}`);

      // Determine which agents to consult
      const requiredAgents = await this.selectBestAgents(stepType, context, config);
      
      if (requiredAgents.length === 0) {
        console.log(`ℹ️ No agents required for step ${stepId}`);
        return [];
      }

      // Create consultation request
      const consultationRequest: ConsultationRequest = {
        id: uuidv4(),
        workflowExecutionId,
        stepId,
        stepType,
        question: this.generateConsultationQuestion(stepType, context),
        context,
        requiredAgents,
        priority: this.determinePriority(config.triggers),
        timeoutMs: config.timeoutMs,
        createdAt: new Date().toISOString()
      };

      this.activeConsultations.set(consultationRequest.id, consultationRequest);

      // Request consultations from all required agents
      const consultationPromises = requiredAgents.map(agentId =>
        this.requestSingleAgentConsultation(consultationRequest, agentId)
      );

      // Wait for all consultations with timeout
      const results = await Promise.allSettled(
        consultationPromises.map(promise =>
          this.withTimeout(promise, config.timeoutMs)
        )
      );

      // Process results
      const consultationResults: ConsultationResult[] = [];
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          consultationResults.push(result.value);
        } else {
          console.warn(`Consultation failed for agent ${requiredAgents[index]}:`, result);
        }
      });

      // Store results
      this.consultationResults.set(consultationRequest.id, consultationResults);

      // Update metrics
      this.updateMetrics(consultationRequest, consultationResults);

      console.log(`✅ Completed ${consultationResults.length}/${requiredAgents.length} consultations for step ${stepId}`);
      return consultationResults;

    } catch (error) {
      console.error(`❌ Agent consultation failed for step ${stepId}:`, error);
      
      if (config.fallbackBehavior === 'fail') {
        throw error;
      }
      
      return [];
    }
  }

  /**
   * Select the best agents for a given step and context
   */
  private async selectBestAgents(
    stepType: string,
    context: Record<string, any>,
    config: AgentConsultationConfig
  ): Promise<AgentId[]> {
    const selectedAgents: AgentId[] = [];

    // Evaluate each trigger to determine required agents
    for (const trigger of config.triggers) {
      if (await this.evaluateTrigger(trigger, stepType, context)) {
        selectedAgents.push(...trigger.agents);
      }
    }

    // Remove duplicates and limit to maxConsultations
    const uniqueAgents = [...new Set(selectedAgents)];
    return uniqueAgents.slice(0, config.maxConsultations);
  }

  /**
   * Evaluate if a consultation trigger should fire
   */
  private async evaluateTrigger(
    trigger: ConsultationTrigger,
    stepType: string,
    context: Record<string, any>
  ): Promise<boolean> {
    switch (trigger.type) {
      case 'always':
        return true;

      case 'quality_threshold':
        const qualityScore = context.qualityScore || 0;
        return qualityScore < (trigger.condition?.threshold || 0.7);

      case 'feedback_keywords':
        const feedback = context.feedback || '';
        const keywords = trigger.condition?.keywords || [];
        return keywords.some((keyword: string) => 
          feedback.toLowerCase().includes(keyword.toLowerCase())
        );

      case 'content_complexity':
        const complexity = this.assessContentComplexity(context);
        return complexity > (trigger.condition?.threshold || 0.5);

      case 'user_request':
        return context.userRequestedConsultation === true;

      default:
        return false;
    }
  }

  /**
   * Request consultation from a single agent
   */
  private async requestSingleAgentConsultation(
    request: ConsultationRequest,
    agentId: AgentId
  ): Promise<ConsultationResult> {
    const startTime = Date.now();

    try {
      // Create agent session for this workflow execution
      const sessionId = `workflow-${request.workflowExecutionId}`;

      // Request consultation using the existing ConsultationManager (DEPRECATED)
      // const consultationId = await this.consultationManager.requestConsultation(
      //   sessionId,
      //   'workflow-system',
      //   agentId,
      //   request.question,
      //   request.context
      // );

      // Stub implementation for deprecated functionality
      const consultationId = uuidv4();

      // Wait for consultation response (in a real implementation, this would be event-driven)
      const consultation = await this.waitForConsultationResponse(sessionId, consultationId);

      const processingTime = Date.now() - startTime;

      return {
        consultationId,
        agentId,
        response: consultation.response,
        confidence: consultation.confidence || 0.8,
        reasoning: consultation.reasoning,
        suggestions: consultation.suggestions,
        processingTime
      };

    } catch (error) {
      console.error(`Consultation failed for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Wait for consultation response with polling
   */
  private async waitForConsultationResponse(
    sessionId: string,
    consultationId: string,
    maxWaitMs: number = 30000
  ): Promise<Consultation> {
    const startTime = Date.now();
    const pollInterval = 1000; // 1 second

    while (Date.now() - startTime < maxWaitMs) {
      try {
        const consultation = await this.consultationManager.getConsultation(sessionId, consultationId);
        
        if (consultation && consultation.response) {
          return consultation;
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error) {
        console.warn(`Error polling for consultation ${consultationId}:`, error);
      }
    }

    throw new Error(`Consultation ${consultationId} timed out after ${maxWaitMs}ms`);
  }

  /**
   * Generate appropriate consultation question based on step type and context
   */
  private generateConsultationQuestion(stepType: string, context: Record<string, any>): string {
    const topic = context.topic || 'the content';
    const contentType = context.contentType || 'content';

    switch (stepType) {
      case 'AI_GENERATION':
        return `How can I improve the ${contentType} about "${topic}" to better meet user needs and quality standards?`;
      
      case 'KEYWORD_RESEARCH':
        return `What are the best keywords and SEO strategies for ${contentType} about "${topic}"?`;
      
      case 'CONTENT_STRATEGY':
        return `What content strategy would be most effective for "${topic}" given the target audience and goals?`;
      
      default:
        return `Please provide expert advice for improving this ${contentType} about "${topic}".`;
    }
  }

  /**
   * Assess content complexity to determine consultation needs
   */
  private assessContentComplexity(context: Record<string, any>): number {
    let complexity = 0;

    // Factor in content length
    const contentLength = (context.content || '').length;
    if (contentLength > 2000) complexity += 0.3;
    if (contentLength > 5000) complexity += 0.2;

    // Factor in technical terms
    const technicalTerms = ['API', 'algorithm', 'framework', 'implementation', 'architecture'];
    const content = (context.content || '').toLowerCase();
    const technicalTermCount = technicalTerms.filter(term => content.includes(term)).length;
    complexity += technicalTermCount * 0.1;

    // Factor in target audience complexity
    if (context.targetAudience?.includes('technical') || context.targetAudience?.includes('expert')) {
      complexity += 0.2;
    }

    return Math.min(complexity, 1.0);
  }

  /**
   * Determine consultation priority based on triggers
   */
  private determinePriority(triggers: ConsultationTrigger[]): 'low' | 'medium' | 'high' {
    const priorities = triggers.map(t => t.priority);
    
    if (priorities.includes('high')) return 'high';
    if (priorities.includes('medium')) return 'medium';
    return 'low';
  }

  /**
   * Add timeout wrapper to promises
   */
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Consultation timeout')), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * Update consultation metrics
   */
  private updateMetrics(request: ConsultationRequest, results: ConsultationResult[]): void {
    this.metrics.totalConsultations++;
    
    // Update success rate
    const successfulConsultations = results.length;
    const totalRequested = request.requiredAgents.length;
    const currentSuccessRate = successfulConsultations / totalRequested;
    this.metrics.successRate = (this.metrics.successRate + currentSuccessRate) / 2;

    // Update average response time
    const avgResponseTime = results.reduce((sum, r) => sum + r.processingTime, 0) / results.length;
    this.metrics.averageResponseTime = (this.metrics.averageResponseTime + avgResponseTime) / 2;

    // Update agent utilization
    results.forEach(result => {
      this.metrics.agentUtilization[result.agentId] = 
        (this.metrics.agentUtilization[result.agentId] || 0) + 1;
    });

    // Update consultations by trigger
    request.requiredAgents.forEach(agentId => {
      const triggerType = 'workflow-step'; // Simplified for now
      this.metrics.consultationsByTrigger[triggerType] = 
        (this.metrics.consultationsByTrigger[triggerType] || 0) + 1;
    });
  }

  /**
   * Get consultation metrics
   */
  getMetrics(): ConsultationMetrics {
    return { ...this.metrics };
  }

  /**
   * Get consultation results for a request
   */
  getConsultationResults(consultationRequestId: string): ConsultationResult[] {
    return this.consultationResults.get(consultationRequestId) || [];
  }

  /**
   * Clear metrics (for testing)
   */
  clearMetrics(): void {
    this.metrics = {
      totalConsultations: 0,
      successRate: 0,
      averageResponseTime: 0,
      qualityImprovementScore: 0,
      agentUtilization: {},
      consultationsByTrigger: {}
    };
  }
}
