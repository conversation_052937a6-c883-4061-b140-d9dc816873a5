/**
 * Test Script for Approval Flow
 * Tests the fixed infinite loop issue and approval gate functionality
 */

import { getWorkflowEngine, getEnhancedTemplateRegistry } from './singleton';
import { StepStatus, ExecutionStatus } from './types';

export async function testApprovalFlow() {
  console.log('🧪 Starting Approval Flow Test...');

  try {
    // Get instances
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();

    // Test 1: Create workflow from template
    console.log('\n📋 Test 1: Creating workflow from template...');
    const processed = registry.processTemplate('seo-blog-post', 'test-user');
    
    if (!processed) {
      throw new Error('Failed to process template');
    }

    console.log(`✅ Template processed: ${processed.workflow.id}`);
    console.log(`📝 Approval gates: ${processed.approvalGates.length}`);

    // Test 2: Create execution
    console.log('\n🚀 Test 2: Creating execution...');
    const execution = await engine.createExecution(
      processed.workflow,
      {
        topic: 'AI Content Generation Best Practices',
        targetKeywords: ['AI content', 'content generation', 'automation'],
        targetAudience: 'Content marketers and SEO professionals'
      },
      {
        userId: 'test-user',
        source: 'test',
        templateId: 'seo-blog-post'
      }
    );

    console.log(`✅ Execution created: ${execution.id}`);

    // Test 3: Register approval gates
    console.log('\n🔒 Test 3: Registering approval gates...');
    for (const gate of processed.approvalGates) {
      await engine.registerApprovalGate(gate);
      console.log(`✅ Registered approval gate: ${gate.id} for step: ${gate.stepId}`);
    }

    // Test 4: Start workflow execution
    console.log('\n▶️ Test 4: Starting workflow execution...');
    await engine.executeWorkflow(execution.id);

    // Test 5: Monitor execution for 10 seconds
    console.log('\n⏱️ Test 5: Monitoring execution for 10 seconds...');
    
    let iterations = 0;
    const maxIterations = 10;
    
    while (iterations < maxIterations) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      iterations++;

      const currentExecution = await engine.getExecution(execution.id);
      if (!currentExecution) {
        console.log('❌ Execution not found');
        break;
      }

      console.log(`📊 Iteration ${iterations}: Status = ${currentExecution.status}`);
      
      // Check step statuses
      const stepStatuses = Object.entries(currentExecution.stepResults).map(([stepId, result]) => ({
        stepId,
        status: result.status,
        artifactId: result.artifactId
      }));

      stepStatuses.forEach(({ stepId, status, artifactId }) => {
        console.log(`   Step ${stepId}: ${status}${artifactId ? ` (artifact: ${artifactId})` : ''}`);
      });

      // Check if workflow is paused at approval gate
      const waitingSteps = stepStatuses.filter(s => s.status === StepStatus.WAITING_APPROVAL);
      if (waitingSteps.length > 0) {
        console.log(`⏸️ Workflow paused at approval gate(s): ${waitingSteps.map(s => s.stepId).join(', ')}`);
        
        // Test 6: Approve the first waiting step
        if (iterations === 5) { // Approve after 5 seconds
          console.log('\n✅ Test 6: Approving first waiting step...');
          const firstWaitingStep = waitingSteps[0];
          if (firstWaitingStep.artifactId) {
            try {
              await engine.submitApproval(execution.id, firstWaitingStep.artifactId, {
                decision: 'approved',
                feedback: 'Test approval - looks good!',
                reviewerId: 'test-user'
              });
              console.log(`✅ Approved artifact: ${firstWaitingStep.artifactId}`);
            } catch (error) {
              console.log(`❌ Failed to approve: ${error}`);
            }
          }
        }
      }

      // Check if execution completed or failed
      if (currentExecution.status === ExecutionStatus.COMPLETED) {
        console.log('🎉 Workflow completed successfully!');
        break;
      } else if (currentExecution.status === ExecutionStatus.FAILED) {
        console.log('❌ Workflow failed');
        console.log('Error:', currentExecution.error);
        break;
      }
    }

    // Test 7: Final status check
    console.log('\n📋 Test 7: Final status check...');
    const finalExecution = await engine.getExecution(execution.id);
    if (finalExecution) {
      console.log(`Final status: ${finalExecution.status}`);
      console.log(`Steps completed: ${Object.values(finalExecution.stepResults).filter(r => r.status === StepStatus.COMPLETED).length}`);
      console.log(`Steps waiting: ${Object.values(finalExecution.stepResults).filter(r => r.status === StepStatus.WAITING_APPROVAL).length}`);
      console.log(`Steps failed: ${Object.values(finalExecution.stepResults).filter(r => r.status === StepStatus.FAILED).length}`);
    }

    console.log('\n✅ Approval Flow Test Completed!');
    return {
      success: true,
      executionId: execution.id,
      finalStatus: finalExecution?.status,
      message: 'Test completed successfully'
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Test failed'
    };
  }
}

export async function testInfiniteLoopFix() {
  console.log('🔄 Testing Infinite Loop Fix...');

  try {
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();

    // Create a simple workflow with approval gate
    const processed = registry.processTemplate('seo-blog-post', 'test-user');
    if (!processed) {
      throw new Error('Failed to process template');
    }

    const execution = await engine.createExecution(
      processed.workflow,
      { topic: 'Test Topic' },
      { userId: 'test-user', source: 'test' }
    );

    // Register approval gates
    for (const gate of processed.approvalGates) {
      await engine.registerApprovalGate(gate);
    }

    // Start execution
    console.log('Starting execution...');
    const startTime = Date.now();
    await engine.executeWorkflow(execution.id);

    // Monitor for 5 seconds to check for infinite loop
    let apiCallCount = 0;
    const originalFetch = global.fetch;
    
    // Mock fetch to count API calls
    global.fetch = async (...args) => {
      apiCallCount++;
      return originalFetch(...args);
    };

    await new Promise(resolve => setTimeout(resolve, 5000));

    // Restore fetch
    global.fetch = originalFetch;

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Test duration: ${duration}ms`);
    console.log(`API calls made: ${apiCallCount}`);

    // Check if execution is properly paused
    const currentExecution = await engine.getExecution(execution.id);
    const isPaused = currentExecution?.status === ExecutionStatus.PAUSED;
    const hasWaitingSteps = Object.values(currentExecution?.stepResults || {})
      .some(r => r.status === StepStatus.WAITING_APPROVAL);

    console.log(`Execution paused: ${isPaused}`);
    console.log(`Has waiting steps: ${hasWaitingSteps}`);

    // Success criteria:
    // 1. Execution should be paused
    // 2. Should have waiting approval steps
    // 3. Should not make excessive API calls (< 10 in 5 seconds)
    const success = isPaused && hasWaitingSteps && apiCallCount < 10;

    return {
      success,
      duration,
      apiCallCount,
      isPaused,
      hasWaitingSteps,
      message: success ? 'Infinite loop fix verified' : 'Infinite loop may still exist'
    };

  } catch (error) {
    console.error('❌ Infinite loop test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Infinite loop test failed'
    };
  }
}

// Export for use in API endpoints or direct testing
export const approvalFlowTests = {
  testApprovalFlow,
  testInfiniteLoopFix
};
