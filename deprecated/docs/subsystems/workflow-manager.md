# Workflow Manager Subsystem

## 1. Purpose and Responsibilities

The Workflow Manager is responsible for orchestrating the content generation process through a structured workflow of goals and phases. It manages the lifecycle of goals, their dependencies, and ensures that the workflow progresses in a logical and efficient manner.

### 1.1 Primary Responsibilities

- Define and manage goals and their dependencies
- Track goal status and progress
- Enforce sequential workflow progression
- Ensure goals only progress when previous artifacts are approved
- Manage workflow phases and transitions
- Track overall workflow progress
- Coordinate with other subsystems to execute the workflow

## 2. Internal Components

### 2.1 Component Diagram

```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│                    Workflow Manager                     │
│                                                         │
├─────────────────┬───────────────────┬─────────────────┐ │
│                 │                   │                 │ │
│  Goal           │  Phase            │  Progress       │ │
│  Manager        │  Manager          │  Tracker        │ │
│                 │                   │                 │ │
├─────────────────┴───────────────────┴─────────────────┤ │
│                                                       │ │
│                  Core Services                        │ │
│                                                       │ │
├───────────────┬───────────────────┬───────────────────┤ │
│               │                   │                   │ │
│  Dependency   │  Workflow         │  Milestone        │ │
│  Resolver     │  Validator        │  Manager          │ │
│               │                   │                   │ │
└───────────────┴───────────────────┴───────────────────┘ │
                                                          │
└──────────────────────────────────────────────────────────┘
```

### 2.2 Component Descriptions

#### 2.2.1 Goal Manager

Responsible for creating, updating, and tracking goals throughout their lifecycle.

**Functions:**
- Create and define goals
- Activate goals when dependencies are met
- Track goal status and progress
- Complete goals when their criteria are met
- Handle goal dependencies and prerequisites

#### 2.2.2 Phase Manager

Manages the high-level phases of the content generation workflow.

**Functions:**
- Define workflow phases
- Manage phase transitions
- Enforce phase-specific rules and constraints
- Track phase status and progress

#### 2.2.3 Progress Tracker

Tracks and reports on the progress of the overall workflow and individual goals.

**Functions:**
- Calculate progress percentages
- Track milestone completion
- Generate progress reports
- Notify other subsystems of progress changes

#### 2.2.4 Dependency Resolver

Analyzes and resolves dependencies between goals to determine which goals can be activated.

**Functions:**
- Build dependency graphs
- Identify blocked and unblocked goals
- Resolve circular dependencies
- Optimize goal activation order

#### 2.2.5 Workflow Validator

Ensures that the workflow is valid and can be executed successfully.

**Functions:**
- Validate goal definitions
- Check for missing dependencies
- Identify potential deadlocks
- Ensure workflow completeness

#### 2.2.6 Milestone Manager

Tracks significant points in the workflow that represent major achievements.

**Functions:**
- Define workflow milestones
- Track milestone status
- Notify when milestones are reached
- Generate milestone reports

## 3. State Management

### 3.1 State Structure

```typescript
interface WorkflowState {
  // Goal state
  goals: {
    byId: Record<string, Goal>;
    activeIds: string[];
    pendingIds: string[];
    completedIds: string[];
  };
  
  // Phase state
  currentPhase: WorkflowPhase;
  phaseProgress: Record<WorkflowPhase, number>;
  
  // Milestone state
  milestones: Record<string, MilestoneStatus>;
  
  // Overall progress
  overallProgress: number;
  
  // Metadata
  lastUpdated: string;
}

interface Goal {
  id: string;
  type: GoalType;
  description: string;
  dependencies: string[];
  criteria: string[];
  status: GoalStatus;
  progress: number;
  assignedTo?: string;
  artifactIds: string[];
  createdAt: string;
  activatedAt?: string;
  completedAt?: string;
}

enum GoalStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  BLOCKED = 'blocked',
  FAILED = 'failed'
}

enum WorkflowPhase {
  PLANNING = 'planning',
  RESEARCH = 'research',
  CREATION = 'creation',
  REVIEW = 'review',
  FINALIZATION = 'finalization'
}

enum MilestoneStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed'
}
```

### 3.2 State Management Approach

The Workflow Manager uses the central state store to manage its state:

1. **Read-Only Access**: Components read the current state from the store
2. **State Updates**: Components request state updates through the state manager
3. **Transactional Updates**: Updates are performed transactionally to ensure consistency
4. **Event Emission**: State changes trigger events to notify other subsystems

## 4. Event Handling

### 4.1 Events Consumed

| Event Type | Description | Action |
|------------|-------------|--------|
| `artifact.approved` | An artifact has been approved | Check if the associated goal can be completed |
| `artifact.rejected` | An artifact has been rejected | Update goal status and request improvements |
| `goal.dependency.completed` | A goal dependency has been completed | Check if dependent goals can be activated |
| `human.override.workflow` | Human has requested a workflow override | Apply the requested workflow changes |
| `system.error.workflow` | An error occurred in the workflow | Handle error and attempt recovery |

### 4.2 Events Produced

| Event Type | Description | Payload |
|------------|-------------|---------|
| `workflow.goal.created` | A new goal has been created | Goal details |
| `workflow.goal.activated` | A goal has been activated | Goal ID, activation time |
| `workflow.goal.completed` | A goal has been completed | Goal ID, completion time |
| `workflow.phase.changed` | The workflow phase has changed | Old phase, new phase |
| `workflow.milestone.reached` | A milestone has been reached | Milestone ID, timestamp |
| `workflow.progress.updated` | Workflow progress has been updated | Progress percentage |

## 5. Error Handling Strategies

### 5.1 Error Types

1. **Goal Activation Errors**: Errors that occur when activating goals
2. **Dependency Resolution Errors**: Errors in resolving goal dependencies
3. **Phase Transition Errors**: Errors that occur during phase transitions
4. **Progress Tracking Errors**: Errors in tracking and reporting progress

### 5.2 Error Handling Approaches

1. **Retry Mechanism**: Retry failed operations with exponential backoff
2. **Fallback Strategies**: Use alternative approaches when primary methods fail
3. **Error Logging**: Log detailed error information for diagnosis
4. **Human Escalation**: Escalate persistent errors to human operators
5. **State Recovery**: Restore to a known good state when necessary

## 6. Performance Considerations

### 6.1 Optimization Strategies

1. **Efficient Dependency Resolution**: Optimize dependency graph traversal
2. **Caching**: Cache frequently accessed goal and phase information
3. **Batch Processing**: Process related goals in batches
4. **Asynchronous Operations**: Use asynchronous processing for non-blocking operations

### 6.2 Scalability Considerations

1. **Horizontal Scaling**: Design for multiple instances of the Workflow Manager
2. **State Partitioning**: Partition state by session for parallel processing
3. **Load Distribution**: Distribute workflow processing across instances

## 7. Class/Component Diagrams

### 7.1 Class Diagram

```
┌───────────────────┐       ┌───────────────────┐
│ WorkflowManager   │       │ GoalManager       │
├───────────────────┤       ├───────────────────┤
│ - stateStore      │       │ - stateStore      │
│ - eventBus        │       │ - eventBus        │
├───────────────────┤       ├───────────────────┤
│ + defineGoals()   │       │ + createGoal()    │
│ + activateGoal()  │───────│ + activateGoal()  │
│ + completeGoal()  │       │ + updateGoal()    │
│ + processGoals()  │       │ + completeGoal()  │
└───────────────────┘       └───────────────────┘
          │                           │
          │                           │
          ▼                           ▼
┌───────────────────┐       ┌───────────────────┐
│ PhaseManager      │       │ DependencyResolver│
├───────────────────┤       ├───────────────────┤
│ - stateStore      │       │ - stateStore      │
│ - eventBus        │       │ - eventBus        │
├───────────────────┤       ├───────────────────┤
│ + definePhases()  │       │ + buildGraph()    │
│ + changePhase()   │       │ + resolveDepend() │
│ + updateProgress()│       │ + findBlocked()   │
└───────────────────┘       └───────────────────┘
          │                           │
          │                           │
          ▼                           ▼
┌───────────────────┐       ┌───────────────────┐
│ ProgressTracker   │       │ MilestoneManager  │
├───────────────────┤       ├───────────────────┤
│ - stateStore      │       │ - stateStore      │
│ - eventBus        │       │ - eventBus        │
├───────────────────┤       ├───────────────────┤
│ + trackProgress() │       │ + defineMilestone()│
│ + calculateProg() │       │ + updateStatus()  │
│ + reportProgress()│       │ + trackMilestone()│
└───────────────────┘       └───────────────────┘
```

### 7.2 Interaction Diagram

```
┌──────────┐     ┌──────────┐     ┌──────────┐     ┌──────────┐
│          │     │          │     │          │     │          │
│ System   │     │ Workflow │     │ Goal     │     │ Artifact │
│ Controller│     │ Manager  │     │ Manager  │     │ Manager  │
│          │     │          │     │          │     │          │
└────┬─────┘     └────┬─────┘     └────┬─────┘     └────┬─────┘
     │                │                │                │
     │ Initialize     │                │                │
     │───────────────▶│                │                │
     │                │                │                │
     │                │ Define Goals   │                │
     │                │───────────────▶│                │
     │                │                │                │
     │                │                │ Create Artifact│
     │                │                │───────────────▶│
     │                │                │                │
     │                │                │◀ ─ ─ ─ ─ ─ ─ ─ │
     │                │                │ Artifact Created
     │                │                │                │
     │                │◀ ─ ─ ─ ─ ─ ─ ─ │                │
     │                │ Goal Completed │                │
     │                │                │                │
     │◀ ─ ─ ─ ─ ─ ─ ─ │                │                │
     │ Phase Changed  │                │                │
     │                │                │                │
```
