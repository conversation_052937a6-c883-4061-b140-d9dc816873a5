# Implementation Guidelines

This document provides detailed guidelines for implementing the Goal-Based Content Generation System. It covers code structure, key algorithms, testing approaches, and specific implementation considerations for each subsystem.

## 1. General Implementation Guidelines

### 1.1 Code Structure

The system should be organized into the following directory structure:

```
src/
├── core/                  # Core infrastructure
│   ├── state/             # State management
│   ├── events/            # Event system
│   └── errors/            # Error handling
├── subsystems/            # Subsystem implementations
│   ├── workflow/          # Workflow Manager
│   ├── artifacts/         # Artifact Manager
│   ├── feedback/          # Feedback System
│   ├── collaboration/     # Agent Collaboration System
│   ├── human-interaction/ # Human Interaction System
│   └── quality-assurance/ # Quality Assurance System
├── interfaces/            # Interface definitions
├── models/                # Data models
├── utils/                 # Utility functions
├── services/              # External service integrations
│   ├── ai/                # AI service integration
│   └── storage/           # Storage service integration
└── api/                   # API endpoints
    ├── internal/          # Internal API
    └── external/          # External API
```

### 1.2 Coding Standards

1. **TypeScript**: Use TypeScript for all code to ensure type safety
2. **Async/Await**: Use async/await for asynchronous code
3. **Immutability**: Prefer immutable data structures
4. **Error Handling**: Use structured error handling with custom error types
5. **Logging**: Implement comprehensive logging
6. **Documentation**: Document all public APIs and complex algorithms
7. **Testing**: Write tests for all code

### 1.3 Dependency Management

1. **Minimal Dependencies**: Use minimal external dependencies
2. **Dependency Injection**: Use dependency injection for better testability
3. **Version Pinning**: Pin dependency versions for reproducibility

## 2. Core Infrastructure Implementation

### 2.1 State Store Implementation

The State Store should be implemented using a transactional approach with optimistic concurrency control:

```typescript
class StateStore<T> implements IStateStore<T> {
  private storage: IStorageAdapter;
  private cache: Map<string, { state: T, version: number }> = new Map();

  constructor(storage: IStorageAdapter) {
    this.storage = storage;
  }

  async getState(): Promise<T | null> {
    // Try to get from cache first
    const cached = this.cache.get(this.sessionId);
    if (cached) {
      return cached.state;
    }

    // Get from storage
    const result = await this.storage.get(this.sessionId);
    if (!result) {
      return null;
    }

    // Update cache
    this.cache.set(this.sessionId, { state: result.state, version: result.version });

    return result.state;
  }

  async setState(state: T): Promise<void> {
    // Validate state
    this.validateState(state);

    // Update storage
    await this.storage.set(this.sessionId, state);

    // Update cache
    this.cache.set(this.sessionId, { state, version: 1 });
  }

  async updateState(updateFn: (state: T | null) => T | null): Promise<void> {
    // Get current state
    const currentState = await this.getState();

    // Apply update function
    const newState = updateFn(currentState);

    // If null is returned, do nothing
    if (newState === null) {
      return;
    }

    // Set new state
    await this.setState(newState);
  }

  async transactionalUpdate(updateFn: (state: T) => T): Promise<T> {
    let retries = 0;
    const maxRetries = 5;

    while (retries < maxRetries) {
      // Get current state with version
      const result = await this.storage.getWithVersion(this.sessionId);
      if (!result) {
        throw new Error(`State not found for session ${this.sessionId}`);
      }

      const { state, version } = result;

      // Apply update function
      const newState = updateFn(state);

      // Try to update with version check
      try {
        await this.storage.setWithVersion(this.sessionId, newState, version);

        // Update cache
        this.cache.set(this.sessionId, { state: newState, version: version + 1 });

        return newState;
      } catch (error) {
        // If version conflict, retry
        if (error instanceof VersionConflictError) {
          retries++;
          continue;
        }

        throw error;
      }
    }

    throw new Error(`Failed to update state after ${maxRetries} retries`);
  }

  private validateState(state: T): void {
    // Implement state validation
  }
}
```

### 2.2 Event Bus Implementation

The Event Bus should be implemented using an asynchronous, in-memory approach with support for persistence:

```typescript
class EventBus implements IEventBus {
  private handlers: Map<string, Array<(event: SystemEvent) => Promise<void> | void>> = new Map();
  private storage: IEventStorageAdapter;

  constructor(storage: IEventStorageAdapter) {
    this.storage = storage;
  }

  on(eventType: string, handler: (event: SystemEvent) => Promise<void> | void): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }

    this.handlers.get(eventType)!.push(handler);
  }

  async emit(eventType: string, payload: any, source: string, options?: { correlationId?: string, causationId?: string }): Promise<void> {
    const event: SystemEvent = {
      id: uuidv4(),
      type: eventType,
      payload,
      timestamp: new Date().toISOString(),
      source,
      correlationId: options?.correlationId,
      causationId: options?.causationId
    };

    // Store event
    await this.storage.storeEvent(event);

    // Process event
    await this.processEvent(event);
  }

  off(eventType: string, handler: (event: SystemEvent) => Promise<void> | void): void {
    if (!this.handlers.has(eventType)) {
      return;
    }

    const handlers = this.handlers.get(eventType)!;
    const index = handlers.indexOf(handler);

    if (index !== -1) {
      handlers.splice(index, 1);
    }
  }

  private async processEvent(event: SystemEvent): Promise<void> {
    if (!this.handlers.has(event.type)) {
      return;
    }

    const handlers = this.handlers.get(event.type)!;

    // Process handlers in parallel
    await Promise.all(handlers.map(async (handler) => {
      try {
        await handler(event);
      } catch (error) {
        console.error(`Error processing event ${event.type}:`, error);
      }
    }));
  }
}
```

### 2.3 System Controller Implementation

The System Controller should be implemented as a central orchestration component:

```typescript
class SystemController {
  private stateStore: IStateStore<SystemState>;
  private eventBus: IEventBus;
  private workflowManager: IWorkflowManager;
  private artifactManager: IArtifactManager;
  private feedbackSystem: IFeedbackSystem;
  private agentCollaborationSystem: IAgentCollaborationSystem;
  private humanInteractionSystem: IHumanInteractionSystem;
  private qualityAssuranceSystem: IQualityAssuranceSystem;
  private autonomousDecisionFramework: AutonomousDecisionFramework;

  constructor(
    sessionId: string,
    stateStore: IStateStore<SystemState>,
    eventBus: IEventBus,
    options: { autonomousMode?: boolean } = {}
  ) {
    this.stateStore = stateStore;
    this.eventBus = eventBus;

    // Initialize subsystems
    this.workflowManager = new WorkflowManager(sessionId, stateStore, eventBus);
    this.artifactManager = new ArtifactManager(sessionId, stateStore, eventBus);
    this.feedbackSystem = new FeedbackSystem(sessionId, stateStore, eventBus);
    this.agentCollaborationSystem = new AgentCollaborationSystem(sessionId, stateStore, eventBus);
    this.humanInteractionSystem = new HumanInteractionSystem(sessionId, stateStore, eventBus);
    this.qualityAssuranceSystem = new QualityAssuranceSystem(sessionId, stateStore, eventBus);
    this.autonomousDecisionFramework = new AutonomousDecisionFramework();

    // Register event handlers
    this.registerEventHandlers();

    // Initialize state
    this.initializeState(options);
  }

  private registerEventHandlers(): void {
    // Register for events
    this.eventBus.on('artifact.evaluation.completed', this.handleArtifactEvaluation.bind(this));
    this.eventBus.on('workflow.decision.required', this.handleWorkflowDecision.bind(this));
    this.eventBus.on('system.error', this.handleSystemError.bind(this));
  }

  private async initializeState(options: { autonomousMode?: boolean }): Promise<void> {
    await this.stateStore.updateState(state => {
      if (!state) {
        return {
          id: uuidv4(),
          status: 'initialized',
          goals: {
            byId: {},
            activeIds: [],
            pendingIds: [],
            completedIds: []
          },
          artifacts: {},
          feedbackRequests: {},
          feedbackResponses: {},
          humanInteractionRequests: {},
          systemSettings: {
            autonomousMode: options.autonomousMode ?? true,
            confidenceThresholds: {
              'content-strategy': 0.85,
              'content-creation': 0.80,
              'seo-optimization': 0.85,
              'quality-assessment': 0.90
            },
            approvalRequirements: {
              'content-strategy': true,
              'final-content': true
            }
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastUpdated: new Date().toISOString(),
          version: 1
        };
      }

      return state;
    });
  }

  // Event handlers

  private async handleArtifactEvaluation(event: SystemEvent): Promise<void> {
    const { artifactId, evaluation } = event.payload;

    // Get current state
    const state = await this.stateStore.getState();
    if (!state) return;

    const artifact = state.artifacts[artifactId];
    if (!artifact) return;

    // Gather context for autonomy decision
    const context = {
      artifactType: artifact.type,
      phase: await this.workflowManager.getCurrentPhase(),
      confidence: evaluation.confidence || 0.7,
      previousAttempts: artifact.version - 1,
      isHighImpact: this.isHighImpactArtifact(artifact.type),
      decisionPoint: artifact.type === 'content-strategy' ? 'content-strategy-approval' : undefined
    };

    // Evaluate if we can proceed autonomously
    const autonomyDecision = await this.autonomousDecisionFramework.evaluateAutonomyLevel(context);

    // Process based on decision
    if (state.systemSettings.autonomousMode && autonomyDecision.canProceedAutonomously) {
      // Proceed autonomously
      if (evaluation.meetsRequirements) {
        await this.artifactManager.approveArtifact(artifactId, 'system', 'Auto-approved based on evaluation');
      } else {
        await this.artifactManager.rejectArtifact(artifactId, 'system', 'Auto-rejected based on evaluation');
        await this.feedbackSystem.requestImprovements(artifactId, evaluation.feedback);
      }
    } else if (autonomyDecision.requiresHumanIntervention || !state.systemSettings.autonomousMode) {
      // Request human intervention
      await this.humanInteractionSystem.requestApproval(artifactId, {
        evaluation,
        reasoning: autonomyDecision.reasoning,
        urgency: autonomyDecision.requiresHumanIntervention ? 'high' : 'medium'
      });
    } else if (autonomyDecision.recommendHumanReview) {
      // Proceed autonomously but notify humans for optional review
      if (evaluation.meetsRequirements) {
        await this.artifactManager.approveArtifact(artifactId, 'system', 'Auto-approved based on evaluation');
        await this.humanInteractionSystem.notifyForOptionalReview(artifactId, {
          evaluation,
          reasoning: autonomyDecision.reasoning
        });
      } else {
        await this.artifactManager.rejectArtifact(artifactId, 'system', 'Auto-rejected based on evaluation');
        await this.feedbackSystem.requestImprovements(artifactId, evaluation.feedback);
      }
    }
  }

  private isHighImpactArtifact(artifactType: string): boolean {
    return ['content-strategy', 'final-content'].includes(artifactType);
  }

  // Public API

  async initializeSession(params: SessionParams): Promise<string> {
    // Initialize session
    await this.stateStore.updateState(state => {
      if (!state) return state;

      return {
        ...state,
        topic: params.topic,
        contentType: params.contentType || 'blog-article',
        targetAudience: params.targetAudience || 'general audience',
        tone: params.tone || 'informative',
        keywords: params.keywords || [],
        additionalInstructions: params.additionalInstructions,
        status: 'active',
        updatedAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      };
    });

    // Define initial goals
    await this.workflowManager.defineInitialGoals();

    // Process goals
    await this.workflowManager.processGoals();

    // Return session ID
    const state = await this.stateStore.getState();
    return state!.id;
  }

  async processGoals(): Promise<boolean> {
    return await this.workflowManager.processGoals();
  }

  async setAutonomousMode(enabled: boolean): Promise<void> {
    await this.stateStore.updateState(state => {
      if (!state) return state;

      return {
        ...state,
        systemSettings: {
          ...state.systemSettings,
          autonomousMode: enabled
        },
        updatedAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      };
    });

    this.eventBus.emit(
      enabled ? 'system.autonomous.enabled' : 'system.autonomous.disabled',
      { timestamp: new Date().toISOString() },
      'system-controller'
    );
  }
}
```

## 3. Subsystem Implementation Guidelines

### 3.1 Workflow Manager Implementation

The Workflow Manager should be implemented with a focus on goal management and workflow progression:

**Key Algorithms:**

1. **Goal Dependency Resolution**: Implement a topological sort algorithm to resolve goal dependencies
2. **Goal Activation**: Implement a rule-based system for activating goals based on dependencies
3. **Phase Transition**: Implement a state machine for workflow phase transitions

**Implementation Considerations:**

1. Use the existing goal processing logic from `goal-processor-fixed.ts` as a foundation
2. Enhance with support for human approval requirements
3. Implement a more robust dependency tracking system
4. Add support for the autonomous decision framework

### 3.2 Artifact Manager Implementation

The Artifact Manager should be implemented with a focus on artifact lifecycle management:

**Key Algorithms:**

1. **Version Control**: Implement a version control system for artifacts
2. **Approval Workflow**: Implement a state machine for the approval workflow
3. **Content Processing**: Implement content processing for different artifact types

**Implementation Considerations:**

1. Use the existing artifact handling code as a foundation
2. Enhance with support for explicit approval workflow
3. Implement a more robust versioning system
4. Add support for content validation and processing

### 3.3 Feedback System Implementation

The Feedback System should be implemented with a focus on feedback management and incorporation:

**Key Algorithms:**

1. **Feedback Routing**: Implement a routing system for feedback requests
2. **Feedback Incorporation**: Implement an algorithm for incorporating feedback into artifacts
3. **Feedback Analysis**: Implement analysis of feedback patterns and trends

**Implementation Considerations:**

1. Use the existing `FeedbackLoopSystem` as a foundation
2. Enhance with support for human feedback
3. Implement a more robust feedback incorporation mechanism
4. Add support for feedback analysis and tracking

### 3.4 Agent Collaboration System Implementation

The Agent Collaboration System should be implemented with a focus on structured communication and collaboration:

**Key Algorithms:**

1. **Collaboration Protocol**: Implement collaboration protocols for different types of collaboration
2. **Message Routing**: Implement a message routing system with circuit breaker pattern
3. **Collaboration Evaluation**: Implement an algorithm for evaluating collaboration opportunities
4. **Protocol Adaptation**: Design a flexible protocol adapter system for future protocol implementations

**Implementation Considerations:**

1. Use the existing `AgentCommunicationHub` as a foundation
2. Enhance with support for structured collaboration protocols
3. Implement a more robust message routing system
4. Add support for collaboration evaluation and tracking
5. Design the system with clean interfaces to support future protocol adapters
6. Implement a protocol adapter framework that will allow for A2A protocol implementation later

**Protocol Adapter Framework:**

```typescript
// Protocol adapter interface
interface IProtocolAdapter {
  // Convert internal message to protocol-specific format
  adaptOutgoing(message: InternalMessage): ProtocolMessage;

  // Convert protocol-specific message to internal format
  adaptIncoming(message: ProtocolMessage): InternalMessage;

  // Check if this adapter can handle a specific message
  canHandle(message: any): boolean;

  // Get protocol identifier
  getProtocolId(): string;
}

// A2A Protocol adapter (to be implemented later)
class A2AProtocolAdapter implements IProtocolAdapter {
  adaptOutgoing(message: InternalMessage): A2AMessage {
    // Convert internal message to A2A format
    // Implementation to be added later
  }

  adaptIncoming(message: A2AMessage): InternalMessage {
    // Convert A2A message to internal format
    // Implementation to be added later
  }

  canHandle(message: any): boolean {
    // Check if message is A2A format
    return message && message.jsonrpc === '2.0';
  }

  getProtocolId(): string {
    return 'a2a';
  }
}

// Protocol adapter registry
class ProtocolAdapterRegistry {
  private adapters: Map<string, IProtocolAdapter> = new Map();

  registerAdapter(adapter: IProtocolAdapter): void {
    this.adapters.set(adapter.getProtocolId(), adapter);
  }

  getAdapter(protocolId: string): IProtocolAdapter | undefined {
    return this.adapters.get(protocolId);
  }

  findAdapterForMessage(message: any): IProtocolAdapter | undefined {
    for (const adapter of this.adapters.values()) {
      if (adapter.canHandle(message)) {
        return adapter;
      }
    }
    return undefined;
  }
}
```

### 3.5 Human Interaction System Implementation

The Human Interaction System should be implemented with a focus on human-agent interaction:

**Key Algorithms:**

1. **Approval Workflow**: Implement a workflow for human approval requests
2. **Input Processing**: Implement processing of human input
3. **Notification Prioritization**: Implement prioritization of notifications

**Implementation Considerations:**

1. Implement from scratch as this is a new subsystem
2. Design for asynchronous human interaction
3. Implement timeout handling for human responses
4. Add support for notification management

### 3.6 Quality Assurance System Implementation

The Quality Assurance System should be implemented with a focus on quality evaluation and improvement:

**Key Algorithms:**

1. **Quality Evaluation**: Implement evaluation of artifacts against quality criteria
2. **Confidence Calculation**: Implement calculation of confidence in quality evaluations
3. **Improvement Suggestion**: Implement generation of improvement suggestions

**Implementation Considerations:**

1. Use the existing `ArtifactEvaluationService` as a foundation
2. Enhance with support for confidence scoring
3. Implement a more robust evaluation system
4. Add support for improvement suggestion generation

## 4. Testing Approach

### 4.1 Unit Testing

1. **Test Framework**: Use Jest for unit testing
2. **Test Coverage**: Aim for at least 80% code coverage
3. **Test Organization**: Organize tests to mirror the code structure
4. **Mocking**: Use dependency injection and mocking for isolated testing

### 4.2 Integration Testing

1. **Test Scope**: Test interactions between subsystems
2. **Test Environment**: Use a dedicated test environment with mock external services
3. **Test Data**: Use realistic test data
4. **Test Scenarios**: Test common workflows and edge cases

### 4.3 System Testing

1. **Test Scope**: Test the entire system end-to-end
2. **Test Environment**: Use a staging environment that mirrors production
3. **Test Data**: Use production-like data
4. **Test Scenarios**: Test complete content generation workflows

### 4.4 Performance Testing

1. **Test Scope**: Test system performance under load
2. **Test Metrics**: Measure response times, throughput, and resource usage
3. **Test Scenarios**: Test with varying load patterns
4. **Test Analysis**: Identify bottlenecks and optimization opportunities
