// Example: Adapting your existing WorkflowManager to support visual workflows

import { WorkflowManager } from './existing/WorkflowManager';
import { Goal, GoalType } from './existing/types';

// New types for visual workflow support
interface VisualNode {
  id: string;
  type: 'ai-generation' | 'human-review' | 'data-input' | 'publish' | 'decision';
  position: { x: number; y: number };
  data: {
    label: string;
    config: any;
  };
}

interface VisualEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
}

interface VisualWorkflow {
  id: string;
  name: string;
  nodes: VisualNode[];
  edges: VisualEdge[];
}

// Adapter class that bridges visual workflows with your goal-based system
export class VisualWorkflowAdapter {
  constructor(
    private workflowManager: WorkflowManager,
    private stateStore: IStateStore,
    private eventBus: IEventBus
  ) {}

  /**
   * Converts a visual workflow created in React Flow to your goal-based system
   */
  async createWorkflowFromVisual(visualWorkflow: VisualWorkflow): Promise<string> {
    // Step 1: Convert visual nodes to goals
    const goals = this.convertNodesToGoals(visualWorkflow);
    
    // Step 2: Store visual layout for later rendering
    await this.storeVisualLayout(visualWorkflow);
    
    // Step 3: Create goals using existing WorkflowManager
    const goalIds = await this.workflowManager.defineGoals(goals);
    
    // Step 4: Store workflow metadata
    await this.storeWorkflowMetadata(visualWorkflow.id, {
      name: visualWorkflow.name,
      visualWorkflowId: visualWorkflow.id,
      goalIds,
      createdAt: new Date().toISOString()
    });
    
    return visualWorkflow.id;
  }

  /**
   * Converts visual nodes to your existing goal structure
   */
  private convertNodesToGoals(workflow: VisualWorkflow): Partial<Goal>[] {
    const nodeMap = new Map(workflow.nodes.map(n => [n.id, n]));
    const goals: Partial<Goal>[] = [];
    
    // Build dependency graph
    const dependencies = new Map<string, string[]>();
    for (const edge of workflow.edges) {
      if (!dependencies.has(edge.target)) {
        dependencies.set(edge.target, []);
      }
      dependencies.get(edge.target)!.push(edge.source);
    }
    
    // Convert each node to a goal
    for (const node of workflow.nodes) {
      const nodeDeps = dependencies.get(node.id) || [];
      
      goals.push({
        type: this.mapNodeTypeToGoalType(node.type),
        description: node.data.label,
        dependencies: nodeDeps.map(depId => {
          // Map node IDs to goal IDs (will be resolved after creation)
          return `goal_${depId}`;
        }),
        criteria: this.generateGoalCriteria(node),
        metadata: {
          visualNodeId: node.id,
          nodeType: node.type,
          config: node.data.config,
          position: node.position
        }
      });
    }
    
    return goals;
  }

  /**
   * Maps visual node types to your existing goal types
   */
  private mapNodeTypeToGoalType(nodeType: VisualNode['type']): GoalType {
    const mapping: Record<VisualNode['type'], GoalType> = {
      'ai-generation': 'content-generation',
      'human-review': 'review-approval',
      'data-input': 'data-collection',
      'publish': 'content-publishing',
      'decision': 'workflow-decision'
    };
    
    return mapping[nodeType] || 'generic-task';
  }

  /**
   * Generates goal criteria based on node configuration
   */
  private generateGoalCriteria(node: VisualNode): string[] {
    switch (node.type) {
      case 'ai-generation':
        return [
          'Content generated successfully',
          'Meets minimum length requirements',
          'Passes basic quality checks'
        ];
      
      case 'human-review':
        return [
          'Review completed',
          'Decision recorded (approve/reject)',
          'Feedback captured if rejected'
        ];
      
      case 'publish':
        return [
          'Content published to target platform',
          'Publish confirmation received',
          'URL or ID captured'
        ];
      
      default:
        return ['Task completed successfully'];
    }
  }

  /**
   * Executes a visual workflow
   */
  async executeWorkflow(workflowId: string, input: any): Promise<WorkflowExecutionResult> {
    // Create execution context
    const executionId = uuidv4();
    const execution: WorkflowExecution = {
      id: executionId,
      workflowId,
      status: 'running',
      startedAt: new Date().toISOString(),
      context: { input }
    };
    
    // Store execution
    await this.stateStore.updateState(state => ({
      ...state,
      executions: {
        ...state.executions,
        [executionId]: execution
      }
    }));
    
    // Emit workflow started event
    await this.eventBus.emit('workflow.started', {
      workflowId,
      executionId,
      input
    }, 'visual-workflow-adapter');
    
    try {
      // Execute using existing WorkflowManager
      const result = await this.workflowManager.processGoals();
      
      // Update execution status
      await this.updateExecutionStatus(executionId, 'completed', result);
      
      // Emit completion event
      await this.eventBus.emit('workflow.completed', {
        workflowId,
        executionId,
        result
      }, 'visual-workflow-adapter');
      
      return {
        success: true,
        executionId,
        result
      };
    } catch (error) {
      // Handle errors
      await this.updateExecutionStatus(executionId, 'failed', error);
      
      await this.eventBus.emit('workflow.failed', {
        workflowId,
        executionId,
        error: error.message
      }, 'visual-workflow-adapter');
      
      throw error;
    }
  }

  /**
   * Simplified node executor for common operations
   */
  async executeNode(node: VisualNode, context: any): Promise<any> {
    switch (node.type) {
      case 'ai-generation':
        return this.executeAIGeneration(node.data.config, context);
      
      case 'human-review':
        return this.executeHumanReview(node.data.config, context);
      
      case 'publish':
        return this.executePublish(node.data.config, context);
      
      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }
  }

  private async executeAIGeneration(config: any, context: any): Promise<any> {
    const { model, prompt, temperature = 0.7 } = config;
    
    // Use the AI service
    const response = await this.aiService.generate({
      model: model || 'gpt-4',
      prompt: this.interpolatePrompt(prompt, context),
      temperature,
      apiKey: context.apiKeys?.[model]
    });
    
    return {
      content: response,
      model,
      timestamp: new Date().toISOString()
    };
  }

  private async executeHumanReview(config: any, context: any): Promise<any> {
    // Create simple review request
    const reviewId = uuidv4();
    const reviewUrl = `${process.env.BASE_URL}/workflow/unified?step=review&reviewId=${reviewId}`;

    // Store review data
    await this.stateStore.updateState(state => ({
      ...state,
      reviews: {
        ...state.reviews,
        [reviewId]: {
          id: reviewId,
          content: context.content,
          status: 'pending',
          createdAt: new Date().toISOString()
        }
      }
    }));

    // Send notification
    if (config.notifyEmail) {
      await this.emailService.send({
        to: config.notifyEmail,
        subject: 'Content Review Required',
        body: `Please review: ${reviewUrl}`
      });
    }

    // Wait for review completion
    return this.waitForReview(reviewId);
  }

  private async executePublish(config: any, context: any): Promise<any> {
    const { platform, status = 'draft' } = config;
    
    switch (platform) {
      case 'wordpress':
        return this.wordpressIntegration.publish({
          title: context.title,
          content: context.content,
          status,
          siteUrl: config.siteUrl,
          apiKey: config.apiKey
        });
      
      case 'shopify':
        return this.shopifyIntegration.createProduct({
          title: context.title,
          description: context.content,
          price: context.price,
          storeUrl: config.storeUrl,
          apiKey: config.apiKey
        });
      
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Helper to interpolate variables in prompts
   */
  private interpolatePrompt(prompt: string, context: any): string {
    return prompt.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return context[key] || match;
    });
  }
}

// Example usage:
const adapter = new VisualWorkflowAdapter(workflowManager, stateStore, eventBus);

// Create a visual workflow (this would come from React Flow)
const visualWorkflow: VisualWorkflow = {
  id: 'blog-post-workflow',
  name: 'SEO Blog Post Generator',
  nodes: [
    {
      id: 'input-1',
      type: 'data-input',
      position: { x: 100, y: 100 },
      data: {
        label: 'Input Keywords',
        config: { 
          fields: ['keywords', 'topic', 'tone'] 
        }
      }
    },
    {
      id: 'ai-1',
      type: 'ai-generation',
      position: { x: 300, y: 100 },
      data: {
        label: 'Generate Blog Post',
        config: {
          model: 'gpt-4',
          prompt: 'Write a 1500-word blog post about {{topic}} targeting keywords: {{keywords}}',
          temperature: 0.7
        }
      }
    },
    {
      id: 'review-1',
      type: 'human-review',
      position: { x: 500, y: 100 },
      data: {
        label: 'Review & Edit',
        config: {
          notifyEmail: '<EMAIL>'
        }
      }
    },
    {
      id: 'publish-1',
      type: 'publish',
      position: { x: 700, y: 100 },
      data: {
        label: 'Publish to WordPress',
        config: {
          platform: 'wordpress',
          status: 'draft'
        }
      }
    }
  ],
  edges: [
    { id: 'e1', source: 'input-1', target: 'ai-1' },
    { id: 'e2', source: 'ai-1', target: 'review-1' },
    { id: 'e3', source: 'review-1', target: 'publish-1' }
  ]
};

// Create and execute the workflow
const workflowId = await adapter.createWorkflowFromVisual(visualWorkflow);
const result = await adapter.executeWorkflow(workflowId, {
  keywords: ['content marketing', 'AI writing'],
  topic: 'How AI is Transforming Content Marketing',
  tone: 'professional'
});