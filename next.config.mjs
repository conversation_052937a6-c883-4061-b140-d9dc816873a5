import { withPayload } from '@payloadcms/next/withPayload'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your Next.js config here
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  // Exclude deprecated folder from build
  webpack: (config, { isServer }) => {
    // Ignore deprecated files during build
    config.module.rules.push({
      test: /deprecated/,
      use: 'ignore-loader'
    });
    return config;
  },
}

export default withPayload(nextConfig)
